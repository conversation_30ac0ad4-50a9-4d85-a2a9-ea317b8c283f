import re
import os
import json
from datetime import datetime
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils.text import slugify
from django.utils.dateparse import parse_datetime

from accounts.models import User
from common.models import <PERSON>egorie
from livres_electroniques.models import LivreElectron<PERSON>, <PERSON><PERSON><PERSON>, Editeur
from librairie.models import LivrePhysique
from accessoires_bureautique.models import AccessoireBureautique, Marque
from accessoires_informatique.models import AccessoireInformatique, MarqueInformatique
from services.models import Service
from blog.models import Article, CategorieArticle


class Command(BaseCommand):
    help = 'Migre les données de WordPress/WooCommerce vers Django'

    def add_arguments(self, parser):
        parser.add_argument(
            '--sql-file',
            type=str,
            default='localhost.sql',
            help='Chemin vers le fichier SQL WordPress'
        )

    def handle(self, *args, **options):
        sql_file = options['sql_file']
        
        if not os.path.exists(sql_file):
            self.stdout.write(
                self.style.ERROR(f'Fichier {sql_file} non trouvé')
            )
            return

        self.stdout.write('Début de la migration des données WordPress...')
        
        # Lire et parser le fichier SQL
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # Extraire les données
        users_data = self.extract_users(sql_content)
        posts_data = self.extract_posts(sql_content)
        postmeta_data = self.extract_postmeta(sql_content)
        
        # Migrer les données
        self.migrate_users(users_data)
        self.migrate_products(posts_data, postmeta_data)
        self.migrate_blog_posts(posts_data)
        
        self.stdout.write(
            self.style.SUCCESS('Migration terminée avec succès!')
        )

    def extract_users(self, sql_content):
        """Extrait les données des utilisateurs"""
        pattern = r"INSERT INTO `wp7j_users`.*?VALUES\s*(.*?);"
        match = re.search(pattern, sql_content, re.DOTALL)
        
        users = []
        if match:
            values_str = match.group(1)
            # Parser les valeurs des utilisateurs
            user_pattern = r"\((\d+),\s*'([^']+)',\s*'([^']+)',\s*'([^']+)',\s*'([^']+)',\s*'([^']*)',\s*'([^']+)',\s*'([^']*)',\s*(\d+),\s*'([^']*)'\)"
            
            for match in re.finditer(user_pattern, values_str):
                users.append({
                    'id': int(match.group(1)),
                    'login': match.group(2),
                    'password': match.group(3),
                    'nicename': match.group(4),
                    'email': match.group(5),
                    'url': match.group(6),
                    'registered': match.group(7),
                    'activation_key': match.group(8),
                    'status': int(match.group(9)),
                    'display_name': match.group(10)
                })
        
        return users

    def extract_posts(self, sql_content):
        """Extrait les données des posts (produits et articles)"""
        posts = []
        
        # Trouver tous les INSERT INTO wp7j_posts
        pattern = r"INSERT INTO `wp7j_posts`.*?VALUES\s*(.*?);"
        
        for match in re.finditer(pattern, sql_content, re.DOTALL):
            values_str = match.group(1)
            
            # Parser chaque ligne de post
            post_pattern = r"\((\d+),\s*(\d+),\s*'([^']+)',\s*'([^']+)',\s*'(.*?)',\s*'(.*?)',\s*'(.*?)',\s*'([^']+)',\s*'([^']+)',\s*'([^']+)',\s*'([^']*)',\s*'([^']*)',\s*'([^']*)',\s*'([^']*)',\s*'([^']+)',\s*'([^']+)',\s*'([^']*)',\s*(\d+),\s*'([^']*)',\s*(\d+),\s*'([^']+)',\s*'([^']*)',\s*(\d+)\)"
            
            for post_match in re.finditer(post_pattern, values_str):
                posts.append({
                    'ID': int(post_match.group(1)),
                    'post_author': int(post_match.group(2)),
                    'post_date': post_match.group(3),
                    'post_date_gmt': post_match.group(4),
                    'post_content': post_match.group(5),
                    'post_title': post_match.group(6),
                    'post_excerpt': post_match.group(7),
                    'post_status': post_match.group(8),
                    'comment_status': post_match.group(9),
                    'ping_status': post_match.group(10),
                    'post_password': post_match.group(11),
                    'post_name': post_match.group(12),
                    'to_ping': post_match.group(13),
                    'pinged': post_match.group(14),
                    'post_modified': post_match.group(15),
                    'post_modified_gmt': post_match.group(16),
                    'post_content_filtered': post_match.group(17),
                    'post_parent': int(post_match.group(18)),
                    'guid': post_match.group(19),
                    'menu_order': int(post_match.group(20)),
                    'post_type': post_match.group(21),
                    'post_mime_type': post_match.group(22),
                    'comment_count': int(post_match.group(23))
                })
        
        return posts

    def extract_postmeta(self, sql_content):
        """Extrait les métadonnées des posts"""
        postmeta = {}
        
        pattern = r"INSERT INTO `wp7j_postmeta`.*?VALUES\s*(.*?);"
        
        for match in re.finditer(pattern, sql_content, re.DOTALL):
            values_str = match.group(1)
            
            meta_pattern = r"\((\d+),\s*(\d+),\s*'([^']*)',\s*'(.*?)'\)"
            
            for meta_match in re.finditer(meta_pattern, values_str):
                post_id = int(meta_match.group(2))
                meta_key = meta_match.group(3)
                meta_value = meta_match.group(4)
                
                if post_id not in postmeta:
                    postmeta[post_id] = {}
                
                postmeta[post_id][meta_key] = meta_value
        
        return postmeta

    def migrate_users(self, users_data):
        """Migre les utilisateurs WordPress vers Django"""
        self.stdout.write('Migration des utilisateurs...')
        
        for user_data in users_data:
            try:
                # Vérifier si l'utilisateur existe déjà
                if User.objects.filter(email=user_data['email']).exists():
                    continue
                
                # Créer l'utilisateur Django
                user = User.objects.create_user(
                    username=user_data['login'],
                    email=user_data['email'],
                    first_name=user_data['display_name'].split(' ')[0] if user_data['display_name'] else '',
                    last_name=' '.join(user_data['display_name'].split(' ')[1:]) if user_data['display_name'] else '',
                    role='client'  # Par défaut, tous les utilisateurs sont clients
                )
                
                self.stdout.write(f'Utilisateur créé: {user.email}')
                
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'Erreur lors de la création de l\'utilisateur {user_data["email"]}: {e}')
                )

    def migrate_products(self, posts_data, postmeta_data):
        """Migre les produits WooCommerce vers les modèles Django"""
        self.stdout.write('Migration des produits...')
        
        # Créer des catégories par défaut
        cat_livres, _ = Categorie.objects.get_or_create(nom='Livres', defaults={'description': 'Livres et publications'})
        cat_bureau, _ = Categorie.objects.get_or_create(nom='Bureau', defaults={'description': 'Fournitures de bureau'})
        cat_info, _ = Categorie.objects.get_or_create(nom='Informatique', defaults={'description': 'Matériel informatique'})
        
        # Créer des auteurs et éditeurs par défaut
        auteur_defaut, _ = Auteur.objects.get_or_create(
            nom='Auteur',
            prenom='Inconnu',
            defaults={'biographie': 'Auteur non spécifié'}
        )
        
        editeur_defaut, _ = Editeur.objects.get_or_create(
            nom='Éditeur Inconnu',
            defaults={'description': 'Éditeur non spécifié'}
        )
        
        # Créer des marques par défaut
        marque_defaut, _ = Marque.objects.get_or_create(
            nom='Marque Inconnue',
            defaults={'description': 'Marque non spécifiée'}
        )
        
        marque_info_defaut, _ = MarqueInformatique.objects.get_or_create(
            nom='Marque Informatique Inconnue',
            defaults={'description': 'Marque informatique non spécifiée'}
        )
        
        for post in posts_data:
            if post['post_type'] == 'product' and post['post_status'] == 'publish':
                try:
                    # Récupérer les métadonnées du produit
                    meta = postmeta_data.get(post['ID'], {})
                    
                    # Déterminer le prix
                    prix = Decimal('0.00')
                    if '_price' in meta:
                        try:
                            prix = Decimal(meta['_price'])
                        except:
                            prix = Decimal('0.00')
                    
                    # Déterminer le stock
                    stock = 0
                    if '_stock' in meta:
                        try:
                            stock = int(meta['_stock'])
                        except:
                            stock = 0
                    
                    # Déterminer le type de produit basé sur le titre et la description
                    titre = post['post_title'].lower()
                    contenu = post['post_content'].lower()
                    
                    if any(word in titre or word in contenu for word in ['livre', 'كتاب', 'مجلة', 'publication']):
                        # C'est un livre - déterminer si électronique ou physique
                        if any(word in titre or word in contenu for word in ['pdf', 'numérique', 'électronique', 'ebook']):
                            # Livre électronique
                            livre = LivreElectronique.objects.create(
                                nom=post['post_title'],
                                resume=post['post_excerpt'] or post['post_content'][:500],
                                prix=prix,
                                stock=stock,
                                categorie=cat_livres,
                                editeur=editeur_defaut,
                                format_fichier='PDF',
                                langue='AR' if any(c in post['post_title'] for c in 'ابتثجحخدذرزسشصضطظعغفقكلمنهوي') else 'FR'
                            )
                            livre.auteurs.add(auteur_defaut)
                        else:
                            # Livre physique
                            livre = LivrePhysique.objects.create(
                                nom=post['post_title'],
                                resume=post['post_excerpt'] or post['post_content'][:500],
                                prix=prix,
                                stock=stock,
                                categorie=cat_livres,
                                editeur=editeur_defaut,
                                format_livre='BROCHE',
                                etat='NEUF'
                            )
                            livre.auteurs.add(auteur_defaut)
                    
                    elif any(word in titre or word in contenu for word in ['papier', 'stylo', 'crayon', 'bureau', 'classeur', 'chemise']):
                        # Accessoire bureautique
                        AccessoireBureautique.objects.create(
                            nom=post['post_title'],
                            description=post['post_excerpt'] or post['post_content'][:500],
                            prix=prix,
                            stock=stock,
                            categorie=cat_bureau,
                            marque=marque_defaut,
                            type_accessoire='AUTRE'
                        )
                    
                    elif any(word in titre or word in contenu for word in ['souris', 'clavier', 'écran', 'ordinateur', 'usb']):
                        # Accessoire informatique
                        AccessoireInformatique.objects.create(
                            nom=post['post_title'],
                            description=post['post_excerpt'] or post['post_content'][:500],
                            prix=prix,
                            stock=stock,
                            categorie=cat_info,
                            marque=marque_info_defaut,
                            type_accessoire='AUTRE'
                        )
                    
                    else:
                        # Service par défaut
                        Service.objects.create(
                            nom=post['post_title'],
                            description=post['post_excerpt'] or post['post_content'][:500],
                            prix=prix,
                            type_service='AUTRE',
                            duree_estimee='1H'
                        )
                    
                    self.stdout.write(f'Produit migré: {post["post_title"]}')
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f'Erreur lors de la migration du produit {post["post_title"]}: {e}')
                    )

    def migrate_blog_posts(self, posts_data):
        """Migre les articles de blog WordPress vers Django"""
        self.stdout.write('Migration des articles de blog...')
        
        # Créer une catégorie par défaut
        cat_defaut, _ = CategorieArticle.objects.get_or_create(
            nom='Actualités',
            defaults={'description': 'Articles d\'actualités', 'couleur': '#007cba'}
        )
        
        for post in posts_data:
            if post['post_type'] == 'post' and post['post_status'] == 'publish':
                try:
                    # Récupérer l'auteur
                    try:
                        auteur = User.objects.get(id=post['post_author'])
                    except User.DoesNotExist:
                        auteur = User.objects.filter(role='admin').first()
                        if not auteur:
                            continue
                    
                    # Créer l'article
                    Article.objects.create(
                        titre=post['post_title'],
                        slug=slugify(post['post_title']),
                        contenu=post['post_content'],
                        extrait=post['post_excerpt'][:300] if post['post_excerpt'] else post['post_content'][:300],
                        auteur=auteur,
                        categorie=cat_defaut,
                        statut='PUBLIE',
                        date_publication=parse_datetime(post['post_date']) or datetime.now()
                    )
                    
                    self.stdout.write(f'Article migré: {post["post_title"]}')
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f'Erreur lors de la migration de l\'article {post["post_title"]}: {e}')
                    )

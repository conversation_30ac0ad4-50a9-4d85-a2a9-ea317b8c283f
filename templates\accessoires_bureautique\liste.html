{% extends 'base.html' %}

{% block title %}Accessoires Bureautique - Librairie TAM{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-pen text-warning"></i>
                Accessoires Bureautique
            </h1>
            <p class="lead">Tout le matériel de bureau dont vous avez besoin : stylos, cahiers, classeurs, calculatrices et plus encore.</p>
        </div>
    </div>

    {% if accessoires %}
        <div class="row">
            {% for accessoire in accessoires %}
                <div class="col-md-4 mb-4">
                    <div class="card product-card h-100">
                        {% if accessoire.image %}
                            <img src="{{ accessoire.image.url }}" class="card-img-top product-image" alt="{{ accessoire.nom }}">
                        {% else %}
                            <img src="https://via.placeholder.com/300x400?text=Accessoire+Bureau" class="card-img-top product-image" alt="{{ accessoire.nom }}">
                        {% endif %}
                        <div class="card-body">
                            <h6 class="card-title">{{ accessoire.nom }}</h6>
                            <p class="text-muted small">{{ accessoire.marque.nom }}</p>
                            <div class="mb-2">
                                <span class="badge bg-warning">{{ accessoire.get_type_accessoire_display }}</span>
                                {% if accessoire.couleur %}<span class="badge bg-secondary">{{ accessoire.get_couleur_display }}</span>{% endif %}
                            </div>
                            {% if accessoire.note_moyenne %}
                                <div class="rating mb-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= accessoire.note_moyenne %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                    <small class="text-muted">({{ accessoire.note_moyenne }})</small>
                                </div>
                            {% endif %}
                            <p class="product-price">{{ accessoire.prix }} €</p>
                            <p class="text-muted small">Stock: {{ accessoire.stock }}</p>
                            <div class="d-flex gap-2">
                                <button class="btn btn-warning btn-sm flex-fill" onclick="ajouterAuPanier({{ accessoire.id }}, 'accessoires_bureautique')">
                                    <i class="fas fa-shopping-cart"></i> Acheter
                                </button>
                                <a href="{% url 'accessoires_bureautique:detail' accessoire.id %}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-warning">
            <i class="fas fa-info-circle"></i>
            Aucun accessoire bureautique trouvé.
            <br>
            <small>Stylos, cahiers, classeurs, calculatrices, agendas et bien plus encore.</small>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function ajouterAuPanier(produitId, boutiqueType) {
    fetch('{% url "common:ajouter_au_panier" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            'produit_id': produitId,
            'boutique_type': boutiqueType,
            'quantite': 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            const navCounter = document.querySelector('.navbar .badge');
            if (navCounter) {
                navCounter.textContent = data.panier_count;
            }
        } else {
            alert('Erreur: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Une erreur est survenue');
    });
}
</script>
{% endblock %}
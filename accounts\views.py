from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.generic import CreateView, UpdateView
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin
from .models import User
from .forms import CustomUserCreationForm, UserProfileForm


class SignUpView(CreateView):
    """Vue d'inscription"""
    model = User
    form_class = CustomUserCreationForm
    template_name = 'registration/signup.html'
    success_url = reverse_lazy('login')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Votre compte a été créé avec succès. Vous pouvez maintenant vous connecter.')
        return response


class ProfileView(LoginRequiredMixin, UpdateView):
    """Vue de profil utilisateur"""
    model = User
    form_class = UserProfileForm
    template_name = 'accounts/profile.html'
    success_url = reverse_lazy('accounts:profile')

    def get_object(self):
        return self.request.user

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Votre profil a été mis à jour avec succès.')
        return response


@login_required
def dashboard(request):
    """Tableau de bord utilisateur"""
    user = request.user
    context = {
        'user': user,
    }

    if user.role == 'admin':
        # Statistiques pour les administrateurs
        context.update({
            'total_users': User.objects.count(),
            'total_vendeurs': User.objects.filter(role='vendeur').count(),
            'total_clients': User.objects.filter(role='client').count(),
        })
        return render(request, 'accounts/dashboard_admin.html', context)

    elif user.role == 'vendeur':
        # Statistiques pour les vendeurs
        from common.models import Commande
        context.update({
            'mes_ventes': Commande.objects.filter(
                lignes__produit_nom__in=user.get_all_products()
            ).distinct().count(),
        })
        return render(request, 'accounts/dashboard_vendeur.html', context)

    else:  # client
        # Informations pour les clients
        from common.models import Commande, Panier
        try:
            panier = user.panier
            context['panier'] = panier
        except Panier.DoesNotExist:
            context['panier'] = None

        context.update({
            'mes_commandes': Commande.objects.filter(client=user),
            'commandes_recentes': Commande.objects.filter(client=user)[:5],
        })
        return render(request, 'accounts/dashboard_client.html', context)


def role_required(allowed_roles):
    """Décorateur pour vérifier les rôles utilisateur"""
    def decorator(view_func):
        def _wrapped_view(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('login')
            if request.user.role not in allowed_roles:
                messages.error(request, 'Vous n\'avez pas les permissions nécessaires pour accéder à cette page.')
                return redirect('accounts:dashboard')
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator

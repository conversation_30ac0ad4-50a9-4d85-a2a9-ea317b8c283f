# Generated by Django 5.1.5 on 2025-08-06 22:21

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accessoires_informatique', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='accessoireinformatique',
            name='vendeur',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_products', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='avisaccessoireinformatique',
            name='accessoire',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='avis', to='accessoires_informatique.accessoireinformatique'),
        ),
        migrations.AddField(
            model_name='avisaccessoireinformatique',
            name='client',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='guidecompatibilite',
            name='accessoire',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='guides_compatibilite', to='accessoires_informatique.accessoireinformatique'),
        ),
        migrations.AddField(
            model_name='accessoireinformatique',
            name='marque',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accessoires_informatique.marqueinformatique'),
        ),
        migrations.AlterUniqueTogether(
            name='avisaccessoireinformatique',
            unique_together={('accessoire', 'client')},
        ),
        migrations.AlterUniqueTogether(
            name='guidecompatibilite',
            unique_together={('accessoire', 'systeme')},
        ),
    ]

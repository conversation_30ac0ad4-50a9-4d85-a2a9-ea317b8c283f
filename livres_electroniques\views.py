from django.shortcuts import render, get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from .models import LivreElectronique, <PERSON>te<PERSON>, Editeur


def liste_livres_electroniques(request):
    """Vue pour afficher la liste des livres électroniques"""
    livres = LivreElectronique.objects.filter(actif=True).select_related('editeur').prefetch_related('auteurs')

    # Filtrage par format
    format_fichier = request.GET.get('format')
    if format_fichier:
        livres = livres.filter(format_fichier=format_fichier)

    # Filtrage par langue
    langue = request.GET.get('langue')
    if langue:
        livres = livres.filter(langue=langue)

    # Recherche
    search = request.GET.get('search')
    if search:
        livres = livres.filter(
            Q(nom__icontains=search) |
            Q(resume__icontains=search) |
            Q(auteurs__nom__icontains=search) |
            Q(auteurs__prenom__icontains=search)
        ).distinct()

    # Tri
    sort = request.GET.get('sort', 'nom')
    if sort == 'prix_asc':
        livres = livres.order_by('prix')
    elif sort == 'prix_desc':
        livres = livres.order_by('-prix')
    elif sort == 'date':
        livres = livres.order_by('-date_creation')
    else:
        livres = livres.order_by('nom')

    # Pagination
    paginator = Paginator(livres, 12)  # 12 livres par page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistiques pour les filtres
    formats = LivreElectronique.objects.filter(actif=True).values_list('format_fichier', flat=True).distinct()
    langues = LivreElectronique.objects.filter(actif=True).values_list('langue', flat=True).distinct()

    context = {
        'livres': page_obj,
        'formats': formats,
        'langues': langues,
        'current_format': format_fichier,
        'current_langue': langue,
        'current_search': search,
        'current_sort': sort,
        'total_livres': livres.count(),
    }

    return render(request, 'livres_electroniques/liste.html', context)


def detail_livre_electronique(request, livre_id):
    """Vue pour afficher le détail d'un livre électronique"""
    livre = get_object_or_404(LivreElectronique, id=livre_id, actif=True)

    # Livres similaires (même catégorie ou même auteur)
    livres_similaires = LivreElectronique.objects.filter(
        Q(categorie=livre.categorie) | Q(auteurs__in=livre.auteurs.all()),
        actif=True
    ).exclude(id=livre.id).distinct()[:4]

    # Avis du livre
    avis = livre.avis.filter(verifie=True).order_by('-date_creation')[:5]

    context = {
        'livre': livre,
        'livres_similaires': livres_similaires,
        'avis': avis,
        'note_moyenne': livre.note_moyenne,
        'nombre_avis': livre.nombre_avis,
    }

    return render(request, 'livres_electroniques/detail.html', context)

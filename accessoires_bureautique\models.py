from django.db import models
from django.conf import settings
from common.models import BaseProduct, Categorie


class Marque(models.Model):
    """Marques d'accessoires bureautique"""

    nom = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    logo = models.ImageField(upload_to='marques/', blank=True, null=True)
    site_web = models.URLField(blank=True)

    def __str__(self):
        return self.nom

    class Meta:
        verbose_name = "Marque"
        verbose_name_plural = "Marques"


class AccessoireBureautique(BaseProduct):
    """Modèle pour les accessoires de bureautique"""

    TYPE_CHOICES = [
        ('stylo', 'Stylos et crayons'),
        ('cahier', 'Cahiers et carnets'),
        ('classeur', 'Classeurs et rangement'),
        ('papeterie', 'Papeterie'),
        ('bureau', 'Accessoires de bureau'),
        ('calculatrice', 'Calculatrices'),
        ('agenda', 'Agendas et planners'),
        ('adhesif', 'Adhésifs et colles'),
        ('correction', 'Correction'),
        ('dessin', 'Matériel de dessin'),
    ]

    COULEUR_CHOICES = [
        ('noir', 'Noir'),
        ('bleu', 'Bleu'),
        ('rouge', 'Rouge'),
        ('vert', 'Vert'),
        ('jaune', 'Jaune'),
        ('blanc', 'Blanc'),
        ('multicolore', 'Multicolore'),
        ('transparent', 'Transparent'),
        ('autre', 'Autre'),
    ]

    # Informations spécifiques
    marque = models.ForeignKey(Marque, on_delete=models.SET_NULL, null=True, blank=True)
    categorie = models.ForeignKey(Categorie, on_delete=models.SET_NULL, null=True, blank=True)
    type_accessoire = models.CharField(max_length=20, choices=TYPE_CHOICES)

    # Caractéristiques
    reference = models.CharField(max_length=50, blank=True, help_text='Référence fabricant')
    couleur = models.CharField(max_length=20, choices=COULEUR_CHOICES, blank=True)
    dimensions = models.CharField(max_length=100, blank=True, help_text='Ex: 21 x 29.7 cm')
    poids = models.CharField(max_length=20, blank=True, help_text='Ex: 150g')
    materiau = models.CharField(max_length=100, blank=True, help_text='Ex: Plastique, Métal, Papier')

    # Spécifications techniques
    specifications = models.TextField(blank=True, help_text='Spécifications techniques détaillées')
    contenu_pack = models.TextField(blank=True, help_text='Contenu du pack/lot')

    # Images supplémentaires
    image_2 = models.ImageField(upload_to='accessoires_bureautique/', blank=True, null=True)
    image_3 = models.ImageField(upload_to='accessoires_bureautique/', blank=True, null=True)

    # Informations commerciales
    garantie = models.CharField(max_length=50, blank=True, help_text='Ex: 2 ans')
    age_minimum = models.PositiveIntegerField(blank=True, null=True, help_text='Âge minimum recommandé')

    # Statistiques
    note_moyenne = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    nombre_avis = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"{self.nom} - {self.marque.nom if self.marque else 'Sans marque'}"

    class Meta:
        verbose_name = "Accessoire bureautique"
        verbose_name_plural = "Accessoires bureautique"


class AvisAccessoireBureautique(models.Model):
    """Avis sur les accessoires bureautique"""

    NOTES_CHOICES = [
        (1, '1 étoile'),
        (2, '2 étoiles'),
        (3, '3 étoiles'),
        (4, '4 étoiles'),
        (5, '5 étoiles'),
    ]

    accessoire = models.ForeignKey(AccessoireBureautique, on_delete=models.CASCADE, related_name='avis')
    client = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    note = models.PositiveSmallIntegerField(choices=NOTES_CHOICES)
    commentaire = models.TextField(blank=True)
    date_creation = models.DateTimeField(auto_now_add=True)
    verifie = models.BooleanField(default=False)

    def __str__(self):
        return f"Avis de {self.client.email} sur {self.accessoire.nom} - {self.note}/5"

    class Meta:
        verbose_name = "Avis accessoire bureautique"
        verbose_name_plural = "Avis accessoires bureautique"
        unique_together = ['accessoire', 'client']
        ordering = ['-date_creation']


class PromotionBureautique(models.Model):
    """Promotions spéciales pour les accessoires bureautique"""

    TYPE_CHOICES = [
        ('pourcentage', 'Pourcentage'),
        ('montant_fixe', 'Montant fixe'),
        ('lot', 'Offre lot'),
    ]

    nom = models.CharField(max_length=100)
    description = models.TextField()
    type_promotion = models.CharField(max_length=20, choices=TYPE_CHOICES)
    valeur = models.DecimalField(max_digits=10, decimal_places=2, help_text='Pourcentage ou montant')
    accessoires = models.ManyToManyField(AccessoireBureautique, related_name='promotions')

    date_debut = models.DateTimeField()
    date_fin = models.DateTimeField()
    actif = models.BooleanField(default=True)

    def __str__(self):
        return self.nom

    class Meta:
        verbose_name = "Promotion bureautique"
        verbose_name_plural = "Promotions bureautique"

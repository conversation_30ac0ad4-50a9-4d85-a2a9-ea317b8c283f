import random
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model

from common.models import Categorie
from livres_electroniques.models import LivreElectron<PERSON>, <PERSON><PERSON><PERSON>, Editeur
from librairie.models import LivrePhysique
from accessoires_bureautique.models import AccessoireBureautique, <PERSON><PERSON>
from accessoires_informatique.models import AccessoireInformatique, MarqueInformatique
from services.models import Service
from blog.models import Article, CategorieArticle

User = get_user_model()


class Command(BaseCommand):
    help = 'Crée des données d\'exemple pour enrichir le catalogue'

    def handle(self, *args, **options):
        self.stdout.write('Création de données d\'exemple...')
        
        self.create_categories()
        self.create_auteurs_editeurs()
        self.create_marques()
        self.create_livres_electroniques()
        self.create_livres_physiques()
        self.create_accessoires_bureautique()
        self.create_accessoires_informatique()
        self.create_services()
        self.create_blog_articles()
        
        self.stdout.write(
            self.style.SUCCESS('Données d\'exemple créées avec succès!')
        )

    def create_categories(self):
        """Crée des catégories supplémentaires"""
        categories = [
            ('Littérature', 'Romans, nouvelles et poésie'),
            ('Sciences', 'Livres scientifiques et techniques'),
            ('Histoire', 'Livres d\'histoire et biographies'),
            ('Éducation', 'Manuels scolaires et universitaires'),
            ('Informatique', 'Livres sur la programmation et l\'informatique'),
        ]
        
        for nom, desc in categories:
            Categorie.objects.get_or_create(nom=nom, defaults={'description': desc})

    def create_auteurs_editeurs(self):
        """Crée des auteurs et éditeurs"""
        auteurs = [
            ('Victor', 'Hugo', 'Écrivain français du XIXe siècle'),
            ('Agatha', 'Christie', 'Romancière britannique de romans policiers'),
            ('Isaac', 'Asimov', 'Écrivain américain de science-fiction'),
            ('J.K.', 'Rowling', 'Auteure britannique de fantasy'),
            ('Stephen', 'King', 'Écrivain américain d\'horreur'),
        ]
        
        for prenom, nom, bio in auteurs:
            Auteur.objects.get_or_create(
                nom=nom, prenom=prenom,
                defaults={'biographie': bio}
            )
        
        editeurs = [
            ('Gallimard', 'Maison d\'édition française prestigieuse'),
            ('Hachette', 'Groupe éditorial français'),
            ('Seuil', 'Maison d\'édition française'),
            ('Flammarion', 'Éditeur français généraliste'),
            ('Dunod', 'Éditeur spécialisé en sciences et techniques'),
        ]
        
        for nom, desc in editeurs:
            Editeur.objects.get_or_create(nom=nom, defaults={'description': desc})

    def create_marques(self):
        """Crée des marques pour les accessoires"""
        marques_bureau = [
            ('Bic', 'Marque française de stylos et fournitures'),
            ('Stabilo', 'Marque allemande de surligneurs et stylos'),
            ('Pilot', 'Marque japonaise d\'instruments d\'écriture'),
            ('Maped', 'Marque française de fournitures scolaires'),
            ('Exacompta', 'Marque française de classement'),
        ]
        
        for nom, desc in marques_bureau:
            Marque.objects.get_or_create(nom=nom, defaults={'description': desc})
        
        marques_info = [
            ('Logitech', 'Marque suisse de périphériques informatiques'),
            ('Microsoft', 'Géant américain de l\'informatique'),
            ('HP', 'Marque américaine d\'ordinateurs et imprimantes'),
            ('Canon', 'Marque japonaise d\'imagerie et optique'),
            ('Samsung', 'Conglomérat sud-coréen d\'électronique'),
        ]
        
        for nom, desc in marques_info:
            MarqueInformatique.objects.get_or_create(nom=nom, defaults={'description': desc})

    def create_livres_electroniques(self):
        """Crée des livres électroniques d\'exemple"""
        vendeur = User.objects.filter(role='admin').first() or User.objects.first()
        if not vendeur:
            return
        
        categorie = Categorie.objects.get_or_create(nom='Littérature')[0]
        auteur = Auteur.objects.first()
        editeur = Editeur.objects.first()
        
        livres = [
            {
                'nom': 'Les Misérables (Version numérique)',
                'resume': 'Chef-d\'œuvre de Victor Hugo, roman social et historique majeur de la littérature française.',
                'prix': Decimal('9.99'),
                'format_fichier': 'EPUB',
                'langue': 'FR',
                'stock': 999
            },
            {
                'nom': 'Le Petit Prince (eBook)',
                'resume': 'Conte poétique et philosophique d\'Antoine de Saint-Exupéry.',
                'prix': Decimal('7.99'),
                'format_fichier': 'PDF',
                'langue': 'FR',
                'stock': 999
            },
            {
                'nom': 'Guide Python pour débutants',
                'resume': 'Apprenez la programmation Python de manière simple et efficace.',
                'prix': Decimal('19.99'),
                'format_fichier': 'PDF',
                'langue': 'FR',
                'stock': 999
            }
        ]
        
        for livre_data in livres:
            livre = LivreElectronique.objects.create(
                **livre_data,
                categorie=categorie,
                editeur=editeur,
                vendeur=vendeur
            )
            livre.auteurs.add(auteur)
            self.stdout.write(f'Livre électronique créé: {livre.nom}')

    def create_livres_physiques(self):
        """Crée des livres physiques d\'exemple"""
        vendeur = User.objects.filter(role='admin').first() or User.objects.first()
        if not vendeur:
            return
        
        categorie = Categorie.objects.get_or_create(nom='Littérature')[0]
        auteur = Auteur.objects.first()
        editeur = Editeur.objects.first()
        
        livres = [
            {
                'nom': 'L\'Étranger - Albert Camus',
                'resume': 'Roman emblématique de la littérature française du XXe siècle.',
                'prix': Decimal('12.50'),
                'format_livre': 'POCHE',
                'etat': 'NEUF',
                'stock': 15
            },
            {
                'nom': 'Le Seigneur des Anneaux - Coffret',
                'resume': 'Trilogie fantasy épique de J.R.R. Tolkien en coffret collector.',
                'prix': Decimal('45.00'),
                'format_livre': 'RELIE',
                'etat': 'NEUF',
                'stock': 8
            },
            {
                'nom': 'Dictionnaire Larousse 2025',
                'resume': 'Dictionnaire de référence avec les dernières évolutions de la langue française.',
                'prix': Decimal('29.90'),
                'format_livre': 'RELIE',
                'etat': 'NEUF',
                'stock': 12
            }
        ]
        
        for livre_data in livres:
            livre = LivrePhysique.objects.create(
                **livre_data,
                categorie=categorie,
                editeur=editeur,
                vendeur=vendeur
            )
            livre.auteurs.add(auteur)
            self.stdout.write(f'Livre physique créé: {livre.nom}')

    def create_accessoires_bureautique(self):
        """Crée des accessoires bureautique d\'exemple"""
        vendeur = User.objects.filter(role='admin').first() or User.objects.first()
        if not vendeur:
            return
        
        categorie = Categorie.objects.get_or_create(nom='Bureau')[0]
        marque = Marque.objects.first()
        
        accessoires = [
            {
                'nom': 'Stylo Bic Cristal - Lot de 10',
                'description': 'Stylos à bille classiques, écriture fluide et régulière.',
                'prix': Decimal('3.50'),
                'type_accessoire': 'STYLO',
                'couleur': 'BLEU',
                'stock': 50
            },
            {
                'nom': 'Cahier spirale A4 - 200 pages',
                'description': 'Cahier à spirale format A4, papier 90g, couverture plastifiée.',
                'prix': Decimal('4.90'),
                'type_accessoire': 'AUTRE',
                'couleur': 'ROUGE',
                'stock': 30
            },
            {
                'nom': 'Calculatrice scientifique Casio',
                'description': 'Calculatrice scientifique avec fonctions avancées pour lycée et université.',
                'prix': Decimal('25.90'),
                'type_accessoire': 'AUTRE',
                'couleur': 'NOIR',
                'stock': 20
            }
        ]
        
        for acc_data in accessoires:
            AccessoireBureautique.objects.create(
                **acc_data,
                categorie=categorie,
                marque=marque,
                vendeur=vendeur
            )
            self.stdout.write(f'Accessoire bureautique créé: {acc_data["nom"]}')

    def create_accessoires_informatique(self):
        """Crée des accessoires informatique d\'exemple"""
        vendeur = User.objects.filter(role='admin').first() or User.objects.first()
        if not vendeur:
            return
        
        categorie = Categorie.objects.get_or_create(nom='Informatique')[0]
        marque = MarqueInformatique.objects.first()
        
        accessoires = [
            {
                'nom': 'Souris optique sans fil Logitech',
                'description': 'Souris ergonomique sans fil avec récepteur USB, autonomie 12 mois.',
                'prix': Decimal('19.90'),
                'type_accessoire': 'SOURIS',
                'compatibilite': 'Windows/Mac/Linux',
                'stock': 25
            },
            {
                'nom': 'Clavier mécanique gaming RGB',
                'description': 'Clavier mécanique avec rétroéclairage RGB personnalisable.',
                'prix': Decimal('89.90'),
                'type_accessoire': 'CLAVIER',
                'compatibilite': 'Windows/Mac',
                'stock': 15
            },
            {
                'nom': 'Webcam HD 1080p avec micro',
                'description': 'Webcam haute définition avec microphone intégré pour visioconférences.',
                'prix': Decimal('39.90'),
                'type_accessoire': 'AUTRE',
                'compatibilite': 'Windows/Mac/Linux',
                'stock': 18
            }
        ]
        
        for acc_data in accessoires:
            AccessoireInformatique.objects.create(
                **acc_data,
                categorie=categorie,
                marque=marque,
                vendeur=vendeur
            )
            self.stdout.write(f'Accessoire informatique créé: {acc_data["nom"]}')

    def create_services(self):
        """Crée des services d\'exemple"""
        vendeur = User.objects.filter(role='admin').first() or User.objects.first()
        if not vendeur:
            return
        
        services = [
            {
                'nom': 'Formation bureautique Word/Excel',
                'description': 'Formation complète aux logiciels Word et Excel, niveau débutant à avancé.',
                'prix': Decimal('150.00'),
                'type_service': 'FORMATION',
                'duree_estimee': '4H',
                'lieu_prestation': 'SUR_SITE'
            },
            {
                'nom': 'Réparation ordinateur portable',
                'description': 'Diagnostic et réparation de pannes matérielles et logicielles.',
                'prix': Decimal('75.00'),
                'type_service': 'REPARATION',
                'duree_estimee': '2H',
                'lieu_prestation': 'ATELIER'
            },
            {
                'nom': 'Installation système et logiciels',
                'description': 'Installation complète du système d\'exploitation et logiciels essentiels.',
                'prix': Decimal('50.00'),
                'type_service': 'INSTALLATION',
                'duree_estimee': '1H30',
                'lieu_prestation': 'SUR_SITE'
            }
        ]
        
        for service_data in services:
            Service.objects.create(
                **service_data,
                vendeur=vendeur,
                disponible_weekend=True,
                disponible_soir=False
            )
            self.stdout.write(f'Service créé: {service_data["nom"]}')

    def create_blog_articles(self):
        """Crée des articles de blog d\'exemple"""
        auteur = User.objects.filter(role='admin').first() or User.objects.first()
        if not auteur:
            return
        
        categorie, _ = CategorieArticle.objects.get_or_create(
            nom='Actualités',
            defaults={'description': 'Actualités de la librairie', 'couleur': '#007cba'}
        )
        
        articles = [
            {
                'titre': 'Nouveautés littéraires de janvier 2025',
                'contenu': 'Découvrez notre sélection des meilleures nouveautés littéraires de ce début d\'année. Des romans captivants aux essais enrichissants, notre équipe a sélectionné pour vous les ouvrages incontournables.',
                'extrait': 'Découvrez notre sélection des meilleures nouveautés littéraires de janvier 2025.',
                'statut': 'PUBLIE'
            },
            {
                'titre': 'Guide d\'achat : choisir ses fournitures de bureau',
                'contenu': 'Comment bien choisir ses fournitures de bureau ? Nos experts vous donnent leurs conseils pour optimiser votre espace de travail avec les bons outils.',
                'extrait': 'Nos experts vous donnent leurs conseils pour bien choisir vos fournitures de bureau.',
                'statut': 'PUBLIE'
            }
        ]
        
        for article_data in articles:
            from django.utils.text import slugify
            Article.objects.create(
                **article_data,
                slug=slugify(article_data['titre']),
                auteur=auteur,
                categorie=categorie
            )
            self.stdout.write(f'Article créé: {article_data["titre"]}')

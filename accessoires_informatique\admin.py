from django.contrib import admin
from .models import MarqueInformatique, AccessoireInformatique, AvisAccessoireInformatique, GuideCompatibilite


@admin.register(MarqueInformatique)
class MarqueInformatiqueAdmin(admin.ModelAdmin):
    list_display = ('nom', 'site_web', 'support_technique', 'nombre_produits')
    search_fields = ('nom', 'description')

    def nombre_produits(self, obj):
        return obj.accessoireinformatique_set.count()
    nombre_produits.short_description = 'Nombre de produits'


@admin.register(AccessoireInformatique)
class AccessoireInformatiqueAdmin(admin.ModelAdmin):
    list_display = ('nom', 'marque', 'type_accessoire', 'compatibilite', 'prix', 'stock', 'actif')
    list_filter = ('type_accessoire', 'compatibilite', 'marque', 'actif', 'categorie')
    search_fields = ('nom', 'description', 'reference', 'modele', 'marque__nom')
    list_editable = ('prix', 'stock', 'actif')
    readonly_fields = ('note_moyenne', 'nombre_avis', 'date_creation', 'date_modification')


@admin.register(AvisAccessoireInformatique)
class AvisAccessoireInformatiqueAdmin(admin.ModelAdmin):
    list_display = ('accessoire', 'client', 'note', 'note_qualite', 'note_facilite_utilisation', 'verifie', 'date_creation')
    list_filter = ('note', 'verifie', 'date_creation')
    search_fields = ('accessoire__nom', 'client__email', 'commentaire')
    list_editable = ('verifie',)


@admin.register(GuideCompatibilite)
class GuideCompatibiliteAdmin(admin.ModelAdmin):
    list_display = ('accessoire', 'systeme', 'version_minimum', 'teste')
    list_filter = ('teste', 'systeme')
    search_fields = ('accessoire__nom', 'systeme', 'notes_compatibilite')
    list_editable = ('teste',)

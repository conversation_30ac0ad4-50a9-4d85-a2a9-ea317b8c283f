# Generated by Django 5.1.5 on 2025-08-06 22:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('common', '__first__'),
    ]

    operations = [
        migrations.CreateModel(
            name='AvisAccessoireBureautique',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.PositiveSmallIntegerField(choices=[(1, '1 étoile'), (2, '2 étoiles'), (3, '3 étoiles'), (4, '4 étoiles'), (5, '5 étoiles')])),
                ('commentaire', models.TextField(blank=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('verifie', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'Avis accessoire bureautique',
                'verbose_name_plural': 'Avis accessoires bureautique',
                'ordering': ['-date_creation'],
            },
        ),
        migrations.CreateModel(
            name='Marque',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='marques/')),
                ('site_web', models.URLField(blank=True)),
            ],
            options={
                'verbose_name': 'Marque',
                'verbose_name_plural': 'Marques',
            },
        ),
        migrations.CreateModel(
            name='PromotionBureautique',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('type_promotion', models.CharField(choices=[('pourcentage', 'Pourcentage'), ('montant_fixe', 'Montant fixe'), ('lot', 'Offre lot')], max_length=20)),
                ('valeur', models.DecimalField(decimal_places=2, help_text='Pourcentage ou montant', max_digits=10)),
                ('date_debut', models.DateTimeField()),
                ('date_fin', models.DateTimeField()),
                ('actif', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Promotion bureautique',
                'verbose_name_plural': 'Promotions bureautique',
            },
        ),
        migrations.CreateModel(
            name='AccessoireBureautique',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('prix', models.DecimalField(decimal_places=2, max_digits=10)),
                ('stock', models.PositiveIntegerField(default=0)),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/')),
                ('actif', models.BooleanField(default=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('date_modification', models.DateTimeField(auto_now=True)),
                ('type_accessoire', models.CharField(choices=[('stylo', 'Stylos et crayons'), ('cahier', 'Cahiers et carnets'), ('classeur', 'Classeurs et rangement'), ('papeterie', 'Papeterie'), ('bureau', 'Accessoires de bureau'), ('calculatrice', 'Calculatrices'), ('agenda', 'Agendas et planners'), ('adhesif', 'Adhésifs et colles'), ('correction', 'Correction'), ('dessin', 'Matériel de dessin')], max_length=20)),
                ('reference', models.CharField(blank=True, help_text='Référence fabricant', max_length=50)),
                ('couleur', models.CharField(blank=True, choices=[('noir', 'Noir'), ('bleu', 'Bleu'), ('rouge', 'Rouge'), ('vert', 'Vert'), ('jaune', 'Jaune'), ('blanc', 'Blanc'), ('multicolore', 'Multicolore'), ('transparent', 'Transparent'), ('autre', 'Autre')], max_length=20)),
                ('dimensions', models.CharField(blank=True, help_text='Ex: 21 x 29.7 cm', max_length=100)),
                ('poids', models.CharField(blank=True, help_text='Ex: 150g', max_length=20)),
                ('materiau', models.CharField(blank=True, help_text='Ex: Plastique, Métal, Papier', max_length=100)),
                ('specifications', models.TextField(blank=True, help_text='Spécifications techniques détaillées')),
                ('contenu_pack', models.TextField(blank=True, help_text='Contenu du pack/lot')),
                ('image_2', models.ImageField(blank=True, null=True, upload_to='accessoires_bureautique/')),
                ('image_3', models.ImageField(blank=True, null=True, upload_to='accessoires_bureautique/')),
                ('garantie', models.CharField(blank=True, help_text='Ex: 2 ans', max_length=50)),
                ('age_minimum', models.PositiveIntegerField(blank=True, help_text='Âge minimum recommandé', null=True)),
                ('note_moyenne', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('nombre_avis', models.PositiveIntegerField(default=0)),
                ('categorie', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.categorie')),
            ],
            options={
                'verbose_name': 'Accessoire bureautique',
                'verbose_name_plural': 'Accessoires bureautique',
            },
        ),
    ]

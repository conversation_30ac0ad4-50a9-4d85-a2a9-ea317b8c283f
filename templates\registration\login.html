{% extends 'base.html' %}
{% load django_bootstrap5 %}

{% block title %}Connexion - Librairie TAM{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-body">
                    <h2 class="card-title text-center mb-4">
                        <i class="fas fa-sign-in-alt text-primary"></i>
                        Connexion
                    </h2>

                    <form method="post">
                        {% csrf_token %}
                        {% bootstrap_form form %}

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i> Se connecter
                            </button>
                        </div>
                    </form>

                    <hr>

                    <div class="text-center">
                        <p class="mb-2">
                            <a href="{% url 'accounts:signup' %}" class="text-decoration-none">
                                Pas encore de compte ? S'inscrire
                            </a>
                        </p>
                        <p class="mb-0">
                            <a href="#" class="text-muted text-decoration-none">
                                Mot de passe oublié ?
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
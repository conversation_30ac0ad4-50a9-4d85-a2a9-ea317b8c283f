from django.db import models
from django.conf import settings
from common.models import BaseProduct, Categorie


class Auteur(models.Model):
    """Modèle pour les auteurs de livres"""

    nom = models.CharField(max_length=100)
    prenom = models.CharField(max_length=100)
    biographie = models.TextField(blank=True)
    photo = models.ImageField(upload_to='auteurs/', blank=True, null=True)
    site_web = models.URLField(blank=True)
    date_naissance = models.DateField(blank=True, null=True)
    nationalite = models.CharField(max_length=50, blank=True)

    def __str__(self):
        return f"{self.prenom} {self.nom}"

    @property
    def nom_complet(self):
        return f"{self.prenom} {self.nom}"

    class Meta:
        verbose_name = "Auteur"
        verbose_name_plural = "Auteurs"
        ordering = ['nom', 'prenom']


class Editeur(models.Model):
    """Modèle pour les éditeurs"""

    nom = models.Char<PERSON>ield(max_length=100, unique=True)
    description = models.TextField(blank=True)
    site_web = models.URLField(blank=True)
    logo = models.ImageField(upload_to='editeurs/', blank=True, null=True)

    def __str__(self):
        return self.nom

    class Meta:
        verbose_name = "Éditeur"
        verbose_name_plural = "Éditeurs"


class LivreElectronique(BaseProduct):
    """Modèle pour les livres électroniques"""

    FORMAT_CHOICES = [
        ('pdf', 'PDF'),
        ('epub', 'EPUB'),
        ('mobi', 'MOBI'),
        ('azw', 'AZW'),
    ]

    LANGUE_CHOICES = [
        ('fr', 'Français'),
        ('en', 'Anglais'),
        ('es', 'Espagnol'),
        ('de', 'Allemand'),
        ('it', 'Italien'),
    ]

    # Informations spécifiques aux livres
    isbn = models.CharField(max_length=13, unique=True, blank=True, null=True)
    auteurs = models.ManyToManyField(Auteur, related_name='livres_electroniques')
    editeur = models.ForeignKey(Editeur, on_delete=models.SET_NULL, null=True, blank=True)
    categorie = models.ForeignKey(Categorie, on_delete=models.SET_NULL, null=True, blank=True)

    # Détails du livre électronique
    format_fichier = models.CharField(max_length=10, choices=FORMAT_CHOICES, default='pdf')
    taille_fichier = models.CharField(max_length=20, blank=True, help_text='Ex: 2.5 MB')
    nombre_pages = models.PositiveIntegerField(blank=True, null=True)
    langue = models.CharField(max_length=5, choices=LANGUE_CHOICES, default='fr')

    # Fichiers
    fichier_livre = models.FileField(upload_to='livres_electroniques/', blank=True, null=True)
    extrait_gratuit = models.FileField(upload_to='extraits/', blank=True, null=True)
    couverture = models.ImageField(upload_to='couvertures/', blank=True, null=True)

    # Métadonnées
    date_publication = models.DateField(blank=True, null=True)
    resume = models.TextField(help_text='Résumé du livre')
    table_matieres = models.TextField(blank=True, help_text='Table des matières')

    # Statistiques
    nombre_telechargements = models.PositiveIntegerField(default=0)
    note_moyenne = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    nombre_avis = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"{self.nom} ({self.format_fichier.upper()})"

    @property
    def auteurs_list(self):
        return ", ".join([str(auteur) for auteur in self.auteurs.all()])

    class Meta:
        verbose_name = "Livre électronique"
        verbose_name_plural = "Livres électroniques"


class AvisLivre(models.Model):
    """Avis et notes sur les livres électroniques"""

    NOTES_CHOICES = [
        (1, '1 étoile'),
        (2, '2 étoiles'),
        (3, '3 étoiles'),
        (4, '4 étoiles'),
        (5, '5 étoiles'),
    ]

    livre = models.ForeignKey(LivreElectronique, on_delete=models.CASCADE, related_name='avis')
    client = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    note = models.PositiveSmallIntegerField(choices=NOTES_CHOICES)
    commentaire = models.TextField(blank=True)
    date_creation = models.DateTimeField(auto_now_add=True)
    verifie = models.BooleanField(default=False, help_text='Avis vérifié par un modérateur')

    def __str__(self):
        return f"Avis de {self.client.email} sur {self.livre.nom} - {self.note}/5"

    class Meta:
        verbose_name = "Avis livre"
        verbose_name_plural = "Avis livres"
        unique_together = ['livre', 'client']
        ordering = ['-date_creation']


class TelechargementLivre(models.Model):
    """Historique des téléchargements de livres électroniques"""

    livre = models.ForeignKey(LivreElectronique, on_delete=models.CASCADE, related_name='telechargements')
    client = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    date_telechargement = models.DateTimeField(auto_now_add=True)
    adresse_ip = models.GenericIPAddressField()

    def __str__(self):
        return f"Téléchargement de {self.livre.nom} par {self.client.email}"

    class Meta:
        verbose_name = "Téléchargement livre"
        verbose_name_plural = "Téléchargements livres"
        ordering = ['-date_telechargement']

from django.db import models
from django.conf import settings
from common.models import BaseProduct, Categorie


class MarqueInformatique(models.Model):
    """Marques d'accessoires informatique"""

    nom = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    logo = models.ImageField(upload_to='marques_informatique/', blank=True, null=True)
    site_web = models.URLField(blank=True)
    support_technique = models.URLField(blank=True)

    def __str__(self):
        return self.nom

    class Meta:
        verbose_name = "Marque informatique"
        verbose_name_plural = "Marques informatique"


class AccessoireInformatique(BaseProduct):
    """Modèle pour les accessoires informatique"""

    TYPE_CHOICES = [
        ('souris', 'Souris'),
        ('clavier', 'Claviers'),
        ('ecran', 'Écrans et moniteurs'),
        ('casque', 'Casques et audio'),
        ('webcam', 'Webcams'),
        ('stockage', 'Stockage (USB, disques)'),
        ('cable', 'Câbles et adaptateurs'),
        ('hub', 'Hubs et docks'),
        ('protection', 'Protection et nettoyage'),
        ('gaming', 'Gaming'),
        ('reseau', 'Réseau'),
        ('alimentation', 'Alimentation'),
        ('refroidissement', 'Refroidissement'),
        ('autre', 'Autres accessoires'),
    ]

    COMPATIBILITE_CHOICES = [
        ('windows', 'Windows'),
        ('mac', 'Mac'),
        ('linux', 'Linux'),
        ('android', 'Android'),
        ('ios', 'iOS'),
        ('universel', 'Universel'),
    ]

    # Informations spécifiques
    marque = models.ForeignKey(MarqueInformatique, on_delete=models.SET_NULL, null=True, blank=True)
    categorie = models.ForeignKey(Categorie, on_delete=models.SET_NULL, null=True, blank=True)
    type_accessoire = models.CharField(max_length=20, choices=TYPE_CHOICES)

    # Caractéristiques techniques
    reference = models.CharField(max_length=50, blank=True, help_text='Référence fabricant')
    modele = models.CharField(max_length=100, blank=True)
    couleur = models.CharField(max_length=50, blank=True)
    dimensions = models.CharField(max_length=100, blank=True, help_text='Ex: 12 x 8 x 3 cm')
    poids = models.CharField(max_length=20, blank=True, help_text='Ex: 150g')

    # Compatibilité et connectivité
    compatibilite = models.CharField(max_length=20, choices=COMPATIBILITE_CHOICES, default='universel')
    connectivite = models.CharField(max_length=100, blank=True, help_text='Ex: USB-C, Bluetooth 5.0, WiFi')
    alimentation = models.CharField(max_length=100, blank=True, help_text='Ex: USB, Batterie, Secteur')

    # Spécifications détaillées
    specifications_techniques = models.TextField(blank=True)
    contenu_boite = models.TextField(blank=True, help_text='Contenu de la boîte')

    # Images
    image_2 = models.ImageField(upload_to='accessoires_informatique/', blank=True, null=True)
    image_3 = models.ImageField(upload_to='accessoires_informatique/', blank=True, null=True)
    image_4 = models.ImageField(upload_to='accessoires_informatique/', blank=True, null=True)

    # Informations commerciales
    garantie = models.CharField(max_length=50, blank=True, help_text='Ex: 2 ans constructeur')
    certification = models.CharField(max_length=100, blank=True, help_text='Ex: CE, FCC, RoHS')

    # Statistiques
    note_moyenne = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    nombre_avis = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"{self.nom} - {self.marque.nom if self.marque else 'Sans marque'}"

    class Meta:
        verbose_name = "Accessoire informatique"
        verbose_name_plural = "Accessoires informatique"


class AvisAccessoireInformatique(models.Model):
    """Avis sur les accessoires informatique"""

    NOTES_CHOICES = [
        (1, '1 étoile'),
        (2, '2 étoiles'),
        (3, '3 étoiles'),
        (4, '4 étoiles'),
        (5, '5 étoiles'),
    ]

    accessoire = models.ForeignKey(AccessoireInformatique, on_delete=models.CASCADE, related_name='avis')
    client = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    note = models.PositiveSmallIntegerField(choices=NOTES_CHOICES)
    commentaire = models.TextField(blank=True)
    date_creation = models.DateTimeField(auto_now_add=True)
    verifie = models.BooleanField(default=False)

    # Critères spécifiques
    note_qualite = models.PositiveSmallIntegerField(choices=NOTES_CHOICES, blank=True, null=True)
    note_facilite_utilisation = models.PositiveSmallIntegerField(choices=NOTES_CHOICES, blank=True, null=True)
    note_rapport_qualite_prix = models.PositiveSmallIntegerField(choices=NOTES_CHOICES, blank=True, null=True)

    def __str__(self):
        return f"Avis de {self.client.email} sur {self.accessoire.nom} - {self.note}/5"

    class Meta:
        verbose_name = "Avis accessoire informatique"
        verbose_name_plural = "Avis accessoires informatique"
        unique_together = ['accessoire', 'client']
        ordering = ['-date_creation']


class GuideCompatibilite(models.Model):
    """Guide de compatibilité pour les accessoires informatique"""

    accessoire = models.ForeignKey(AccessoireInformatique, on_delete=models.CASCADE, related_name='guides_compatibilite')
    systeme = models.CharField(max_length=100, help_text='Ex: Windows 11, macOS Monterey')
    version_minimum = models.CharField(max_length=50, blank=True)
    notes_compatibilite = models.TextField(blank=True)
    teste = models.BooleanField(default=False)

    def __str__(self):
        return f"Compatibilité {self.accessoire.nom} avec {self.systeme}"

    class Meta:
        verbose_name = "Guide compatibilité"
        verbose_name_plural = "Guides compatibilité"
        unique_together = ['accessoire', 'systeme']

from django.contrib import admin
from .models import Service, ReservationService, AvisService, CreneauDisponibilite


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ('nom', 'type_service', 'duree_estimee', 'lieu_prestation', 'prix', 'actif')
    list_filter = ('type_service', 'duree_estimee', 'lieu_prestation', 'actif', 'disponible_weekend', 'disponible_soir')
    search_fields = ('nom', 'description', 'deroulement')
    list_editable = ('prix', 'actif')
    readonly_fields = ('note_moyenne', 'nombre_avis', 'nombre_prestations', 'date_creation', 'date_modification')


@admin.register(ReservationService)
class ReservationServiceAdmin(admin.ModelAdmin):
    list_display = ('service', 'client', 'prestataire', 'statut', 'date_souhaitee', 'date_confirmee')
    list_filter = ('statut', 'date_souhaitee')
    search_fields = ('service__nom', 'client__email', 'description_besoin')
    list_editable = ('statut', 'prestataire')
    readonly_fields = ('date_creation', 'date_modification')


@admin.register(AvisService)
class AvisServiceAdmin(admin.ModelAdmin):
    list_display = ('service', 'client', 'note_globale', 'note_ponctualite', 'note_competence', 'recommande', 'verifie')
    list_filter = ('note_globale', 'recommande', 'verifie', 'date_creation')
    search_fields = ('service__nom', 'client__email', 'commentaire')
    list_editable = ('verifie',)
    readonly_fields = ('date_creation',)


@admin.register(CreneauDisponibilite)
class CreneauDisponibiliteAdmin(admin.ModelAdmin):
    list_display = ('service', 'prestataire', 'jour_semaine', 'heure_debut', 'heure_fin', 'actif')
    list_filter = ('jour_semaine', 'actif', 'service__type_service')
    search_fields = ('service__nom', 'prestataire__email')
    list_editable = ('actif',)

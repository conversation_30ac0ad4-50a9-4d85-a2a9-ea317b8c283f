from django.contrib import admin
from .models import LivrePhysique, AvisLivrePhysique, Collection, Reservation
from livres_electroniques.models import Auteur, Editeur


@admin.register(LivrePhysique)
class LivrePhysiqueAdmin(admin.ModelAdmin):
    list_display = ('nom', 'auteurs_display', 'editeur', 'format_livre', 'prix', 'stock', 'etat', 'actif')
    list_filter = ('format_livre', 'etat', 'occasion', 'actif', 'editeur', 'categorie')
    search_fields = ('nom', 'isbn', 'resume', 'auteurs__nom', 'auteurs__prenom')
    list_editable = ('prix', 'stock', 'actif')
    filter_horizontal = ('auteurs',)
    readonly_fields = ('note_moyenne', 'nombre_avis', 'date_creation', 'date_modification')

    def auteurs_display(self, obj):
        return obj.auteurs_list
    auteurs_display.short_description = 'Auteurs'


@admin.register(AvisLivrePhysique)
class AvisLivrePhysiqueAdmin(admin.ModelAdmin):
    list_display = ('livre', 'client', 'note', 'verifie', 'date_creation')
    list_filter = ('note', 'verifie', 'date_creation')
    search_fields = ('livre__nom', 'client__email', 'commentaire')
    list_editable = ('verifie',)


@admin.register(Collection)
class CollectionAdmin(admin.ModelAdmin):
    list_display = ('nom', 'editeur')
    search_fields = ('nom', 'description', 'editeur__nom')
    list_filter = ('editeur',)


@admin.register(Reservation)
class ReservationAdmin(admin.ModelAdmin):
    list_display = ('livre', 'client', 'statut', 'date_reservation', 'date_expiration')
    list_filter = ('statut', 'date_reservation', 'date_expiration')
    search_fields = ('livre__nom', 'client__email')
    list_editable = ('statut',)
    readonly_fields = ('date_reservation',)

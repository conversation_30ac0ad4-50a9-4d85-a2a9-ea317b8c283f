# Generated by Django 5.1.5 on 2025-08-06 22:24

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Boutique',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100)),
                ('type_boutique', models.CharField(choices=[('livres_electroniques', 'Livres Électroniques'), ('librairie', 'Librairie'), ('accessoires_bureautique', 'Accessoires Bureautique'), ('accessoires_informatique', 'Accessoires Informatique'), ('services', 'Services')], max_length=30, unique=True)),
                ('description', models.TextField()),
                ('logo', models.ImageField(blank=True, null=True, upload_to='boutiques/')),
                ('banniere', models.ImageField(blank=True, null=True, upload_to='boutiques/bannieres/')),
                ('actif', models.BooleanField(default=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('responsable', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Boutique',
                'verbose_name_plural': 'Boutiques',
            },
        ),
        migrations.CreateModel(
            name='Categorie',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('actif', models.BooleanField(default=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sous_categories', to='common.categorie')),
            ],
            options={
                'verbose_name': 'Catégorie',
                'verbose_name_plural': 'Catégories',
            },
        ),
        migrations.CreateModel(
            name='Commande',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('numero_commande', models.CharField(default=uuid.uuid4, max_length=50, unique=True)),
                ('statut', models.CharField(choices=[('en_attente', 'En attente'), ('confirmee', 'Confirmée'), ('en_preparation', 'En préparation'), ('expediee', 'Expédiée'), ('livree', 'Livrée'), ('annulee', 'Annulée')], default='en_attente', max_length=20)),
                ('total', models.DecimalField(decimal_places=2, max_digits=10)),
                ('date_commande', models.DateTimeField(auto_now_add=True)),
                ('date_livraison_prevue', models.DateTimeField(blank=True, null=True)),
                ('adresse_livraison', models.TextField()),
                ('notes', models.TextField(blank=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commandes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Commande',
                'verbose_name_plural': 'Commandes',
                'ordering': ['-date_commande'],
            },
        ),
        migrations.CreateModel(
            name='LigneCommande',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('produit_nom', models.CharField(max_length=200)),
                ('produit_prix', models.DecimalField(decimal_places=2, max_digits=10)),
                ('quantite', models.PositiveIntegerField()),
                ('sous_total', models.DecimalField(decimal_places=2, max_digits=10)),
                ('commande', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lignes', to='common.commande')),
            ],
            options={
                'verbose_name': 'Ligne de commande',
                'verbose_name_plural': 'Lignes de commande',
            },
        ),
        migrations.CreateModel(
            name='Panier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('date_modification', models.DateTimeField(auto_now=True)),
                ('client', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='panier', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Panier',
                'verbose_name_plural': 'Paniers',
            },
        ),
        migrations.CreateModel(
            name='ItemPanier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('produit_nom', models.CharField(max_length=200)),
                ('produit_prix', models.DecimalField(decimal_places=2, max_digits=10)),
                ('produit_id', models.PositiveIntegerField()),
                ('boutique_type', models.CharField(max_length=30)),
                ('quantite', models.PositiveIntegerField(default=1)),
                ('date_ajout', models.DateTimeField(auto_now_add=True)),
                ('panier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='common.panier')),
            ],
            options={
                'verbose_name': 'Item panier',
                'verbose_name_plural': 'Items panier',
                'unique_together': {('panier', 'produit_id', 'boutique_type')},
            },
        ),
    ]

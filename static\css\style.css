/* Custom CSS for Librairie TAM */

/* Variables CSS */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #fff !important;
}

/* Cards */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

/* Buttons */
.btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    min-height: 60vh;
}

/* Product Cards */
.product-card {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.product-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 5px 15px rgba(0,123,255,0.1);
}

.product-image {
    height: 200px;
    object-fit: cover;
    width: 100%;
}

.product-price {
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* Rating Stars */
.rating {
    color: #ffc107;
}

.rating .fa-star {
    margin-right: 2px;
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* Footer */
footer {
    margin-top: auto;
}

footer a {
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--primary-color) !important;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2rem;
    }

    .hero-section .lead {
        font-size: 1rem;
    }
}

/* Utility Classes */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.bg-primary-custom {
    background-color: var(--primary-color) !important;
}

.shadow-custom {
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Blog Styles */
.blog-post {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 2rem;
    margin-bottom: 2rem;
}

.blog-post:last-child {
    border-bottom: none;
}

.blog-meta {
    color: #6c757d;
    font-size: 0.9rem;
}

.blog-tag {
    background-color: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    text-decoration: none;
    margin-right: 0.5rem;
}

.blog-tag:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Dashboard Styles */
.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}

.dashboard-stat {
    font-size: 2rem;
    font-weight: bold;
}

/* Search Bar */
.search-bar {
    position: relative;
}

.search-bar .form-control {
    padding-right: 50px;
}

.search-bar .btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 50%;
    width: 40px;
    height: 40px;
}
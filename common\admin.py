from django.contrib import admin
from .models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, LigneCommande


@admin.register(Categorie)
class CategorieAdmin(admin.ModelAdmin):
    list_display = ('nom', 'description')
    search_fields = ('nom', 'description')


@admin.register(Boutique)
class BoutiqueAdmin(admin.ModelAdmin):
    list_display = ('nom', 'type_boutique', 'actif', 'responsable', 'date_creation')
    list_filter = ('type_boutique', 'actif', 'date_creation')
    search_fields = ('nom', 'description')
    list_editable = ('actif',)


class ItemPanierInline(admin.TabularInline):
    model = ItemPanier
    extra = 0
    readonly_fields = ('sous_total',)


@admin.register(Panier)
class PanierAdmin(admin.ModelAdmin):
    list_display = ('client', 'nombre_items', 'total', 'date_creation', 'date_modification')
    list_filter = ('date_creation', 'date_modification')
    search_fields = ('client__email', 'client__first_name', 'client__last_name')
    readonly_fields = ('total', 'nombre_items')
    inlines = [ItemPanierInline]


class LigneCommandeInline(admin.TabularInline):
    model = LigneCommande
    extra = 0
    readonly_fields = ('sous_total',)


@admin.register(Commande)
class CommandeAdmin(admin.ModelAdmin):
    list_display = ('numero_commande', 'client', 'statut', 'total')
    list_filter = ('statut',)
    search_fields = ('numero_commande', 'client__email', 'client__first_name', 'client__last_name')
    list_editable = ('statut',)
    readonly_fields = ('numero_commande', 'total')
    inlines = [LigneCommandeInline]

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        elif request.user.role == 'admin':
            return qs
        elif request.user.role == 'vendeur':
            return qs.filter(lignes__produit_nom__icontains=request.user.username)  # Simplification
        else:
            return qs.filter(client=request.user)


@admin.register(ItemPanier)
class ItemPanierAdmin(admin.ModelAdmin):
    list_display = ('panier', 'produit_nom', 'produit_prix', 'quantite', 'sous_total', 'date_ajout')
    list_filter = ('boutique_type', 'date_ajout')
    search_fields = ('produit_nom', 'panier__client__email')
    readonly_fields = ('sous_total',)


@admin.register(LigneCommande)
class LigneCommandeAdmin(admin.ModelAdmin):
    list_display = ('commande', 'produit_nom', 'produit_prix', 'quantite', 'sous_total')
    list_filter = ('commande__statut',)
    search_fields = ('produit_nom', 'commande__numero_commande', 'commande__client__email')
    readonly_fields = ('sous_total',)
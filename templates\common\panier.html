{% extends 'base.html' %}
{% load django_bootstrap5 %}

{% block title %}Mon Panier - Librairie TAM{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-shopping-cart text-primary"></i>
                Mon <PERSON>
            </h1>
        </div>
    </div>

    {% if items %}
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Articles dans votre panier ({{ nombre_items }})</h5>
                    </div>
                    <div class="card-body">
                        {% for item in items %}
                            <div class="row align-items-center border-bottom py-3" id="item-{{ item.id }}">
                                <div class="col-md-6">
                                    <h6 class="mb-1">{{ item.produit_nom }}</h6>
                                    <small class="text-muted">{{ item.get_boutique_type_display }}</small>
                                </div>
                                <div class="col-md-2">
                                    <div class="input-group input-group-sm">
                                        <button class="btn btn-outline-secondary" type="button" onclick="modifierQuantite({{ item.id }}, {{ item.quantite|add:'-1' }})">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" class="form-control text-center" value="{{ item.quantite }}" min="1" id="qty-{{ item.id }}" onchange="modifierQuantite({{ item.id }}, this.value)">
                                        <button class="btn btn-outline-secondary" type="button" onclick="modifierQuantite({{ item.id }}, {{ item.quantite|add:'1' }})">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <span class="fw-bold">{{ item.produit_prix }} €</span>
                                </div>
                                <div class="col-md-2">
                                    <span class="fw-bold text-primary" id="total-{{ item.id }}">{{ item.sous_total }} €</span>
                                </div>
                                <div class="col-md-1">
                                    <a href="{% url 'common:supprimer_du_panier' item.id %}" class="btn btn-outline-danger btn-sm" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet article ?')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'common:vider_panier' %}" class="btn btn-outline-warning" onclick="return confirm('Êtes-vous sûr de vouloir vider votre panier ?')">
                                <i class="fas fa-trash-alt"></i> Vider le panier
                            </a>
                            <a href="{% url 'home' %}" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left"></i> Continuer mes achats
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Résumé de la commande</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Sous-total :</span>
                            <span id="panier-total">{{ total }} €</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Livraison :</span>
                            <span class="text-success">Gratuite</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Total :</strong>
                            <strong class="text-primary" id="total-final">{{ total }} €</strong>
                        </div>

                        <div class="d-grid">
                            <a href="{% url 'common:checkout' %}" class="btn btn-primary btn-lg">
                                <i class="fas fa-credit-card"></i> Passer la commande
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-body">
                        <h6><i class="fas fa-shield-alt text-success"></i> Paiement sécurisé</h6>
                        <p class="small text-muted mb-0">Vos données sont protégées par un cryptage SSL.</p>
                    </div>
                </div>
            </div>
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-5x text-muted mb-3"></i>
            <h3>Votre panier est vide</h3>
            <p class="text-muted">Découvrez nos produits et ajoutez-les à votre panier.</p>
            <a href="{% url 'home' %}" class="btn btn-primary">
                <i class="fas fa-store"></i> Découvrir nos boutiques
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function modifierQuantite(itemId, nouvelleQuantite) {
    if (nouvelleQuantite < 0) return;

    fetch('{% url "common:modifier_quantite_panier" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            'item_id': itemId,
            'quantite': nouvelleQuantite
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (nouvelleQuantite == 0) {
                document.getElementById('item-' + itemId).remove();
            } else {
                document.getElementById('qty-' + itemId).value = nouvelleQuantite;
                document.getElementById('total-' + itemId).textContent = data.item_total + ' €';
            }
            document.getElementById('panier-total').textContent = data.panier_total + ' €';
            document.getElementById('total-final').textContent = data.panier_total + ' €';

            // Mettre à jour le compteur dans la navbar
            const navCounter = document.querySelector('.navbar .badge');
            if (navCounter) {
                navCounter.textContent = data.panier_count;
            }
        } else {
            alert('Erreur: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Une erreur est survenue');
    });
}
</script>
{% endblock %}
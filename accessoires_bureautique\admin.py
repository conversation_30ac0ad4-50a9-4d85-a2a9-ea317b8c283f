from django.contrib import admin
from .models import Marque, AccessoireBureautique, AvisAccessoireBureautique, PromotionBureautique


@admin.register(Marque)
class MarqueAdmin(admin.ModelAdmin):
    list_display = ('nom', 'site_web', 'nombre_produits')
    search_fields = ('nom', 'description')

    def nombre_produits(self, obj):
        return obj.accessoirebureautique_set.count()
    nombre_produits.short_description = 'Nombre de produits'


@admin.register(AccessoireBureautique)
class AccessoireBureautiqueAdmin(admin.ModelAdmin):
    list_display = ('nom', 'marque', 'type_accessoire', 'couleur', 'prix', 'stock', 'actif')
    list_filter = ('type_accessoire', 'couleur', 'marque', 'actif', 'categorie')
    search_fields = ('nom', 'description', 'reference', 'marque__nom')
    list_editable = ('prix', 'stock', 'actif')
    readonly_fields = ('note_moyenne', 'nombre_avis', 'date_creation', 'date_modification')


@admin.register(AvisAccessoireBureautique)
class AvisAccessoireBureautiqueAdmin(admin.ModelAdmin):
    list_display = ('accessoire', 'client', 'note', 'verifie', 'date_creation')
    list_filter = ('note', 'verifie', 'date_creation')
    search_fields = ('accessoire__nom', 'client__email', 'commentaire')
    list_editable = ('verifie',)


@admin.register(PromotionBureautique)
class PromotionBureautiqueAdmin(admin.ModelAdmin):
    list_display = ('nom', 'type_promotion', 'valeur', 'date_debut', 'date_fin', 'actif')
    list_filter = ('type_promotion', 'actif', 'date_debut', 'date_fin')
    search_fields = ('nom', 'description')
    list_editable = ('actif',)
    filter_horizontal = ('accessoires',)

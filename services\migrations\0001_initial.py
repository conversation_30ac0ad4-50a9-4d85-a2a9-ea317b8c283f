# Generated by Django 5.1.5 on 2025-08-06 22:21

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('common', '__first__'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('prix', models.DecimalField(decimal_places=2, max_digits=10)),
                ('stock', models.PositiveIntegerField(default=0)),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/')),
                ('actif', models.BooleanField(default=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('date_modification', models.DateTimeField(auto_now=True)),
                ('type_service', models.CharField(choices=[('formation', 'Formation'), ('consultation', 'Consultation'), ('reparation', 'Réparation'), ('installation', 'Installation'), ('maintenance', 'Maintenance'), ('personnalisation', 'Personnalisation'), ('livraison', 'Livraison'), ('autre', 'Autre service')], max_length=20)),
                ('duree_estimee', models.CharField(choices=[('30min', '30 minutes'), ('1h', '1 heure'), ('2h', '2 heures'), ('demi_journee', 'Demi-journée'), ('journee', 'Journée complète'), ('plusieurs_jours', 'Plusieurs jours'), ('sur_mesure', 'Sur mesure')], max_length=20)),
                ('lieu_prestation', models.CharField(choices=[('domicile', 'À domicile'), ('magasin', 'En magasin'), ('distance', 'À distance'), ('entreprise', 'En entreprise'), ('flexible', 'Flexible')], max_length=20)),
                ('materiel_inclus', models.BooleanField(default=False, help_text='Matériel inclus dans le service')),
                ('deplacement_inclus', models.BooleanField(default=False, help_text='Déplacement inclus')),
                ('prerequis', models.TextField(blank=True, help_text='Prérequis pour bénéficier du service')),
                ('materiel_necessaire', models.TextField(blank=True, help_text='Matériel nécessaire côté client')),
                ('deroulement', models.TextField(help_text='Déroulement du service')),
                ('prix_deplacement', models.DecimalField(decimal_places=2, default=0.0, max_digits=8)),
                ('prix_materiel', models.DecimalField(decimal_places=2, default=0.0, max_digits=8)),
                ('disponible_weekend', models.BooleanField(default=False)),
                ('disponible_soir', models.BooleanField(default=False)),
                ('delai_reservation', models.PositiveIntegerField(default=1, help_text='Délai minimum de réservation en jours')),
                ('image_2', models.ImageField(blank=True, null=True, upload_to='services/')),
                ('image_3', models.ImageField(blank=True, null=True, upload_to='services/')),
                ('note_moyenne', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('nombre_avis', models.PositiveIntegerField(default=0)),
                ('nombre_prestations', models.PositiveIntegerField(default=0)),
                ('categorie', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.categorie')),
                ('vendeur', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_products', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Service',
                'verbose_name_plural': 'Services',
            },
        ),
        migrations.CreateModel(
            name='ReservationService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_souhaitee', models.DateTimeField()),
                ('date_confirmee', models.DateTimeField(blank=True, null=True)),
                ('duree_reelle', models.DurationField(blank=True, null=True)),
                ('adresse_prestation', models.TextField(blank=True)),
                ('telephone_contact', models.CharField(max_length=20)),
                ('description_besoin', models.TextField(help_text='Description détaillée du besoin')),
                ('materiel_client', models.TextField(blank=True, help_text='Matériel disponible côté client')),
                ('contraintes', models.TextField(blank=True, help_text='Contraintes particulières')),
                ('statut', models.CharField(choices=[('demande', 'Demande en cours'), ('confirmee', 'Confirmée'), ('en_cours', 'En cours'), ('terminee', 'Terminée'), ('annulee', 'Annulée'), ('reportee', 'Reportée')], default='demande', max_length=20)),
                ('prix_final', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('notes_prestataire', models.TextField(blank=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('date_modification', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reservations_services', to=settings.AUTH_USER_MODEL)),
                ('prestataire', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='prestations', to=settings.AUTH_USER_MODEL)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reservations', to='services.service')),
            ],
            options={
                'verbose_name': 'Réservation service',
                'verbose_name_plural': 'Réservations services',
                'ordering': ['-date_creation'],
            },
        ),
        migrations.CreateModel(
            name='AvisService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note_globale', models.PositiveSmallIntegerField(choices=[(1, '1 étoile'), (2, '2 étoiles'), (3, '3 étoiles'), (4, '4 étoiles'), (5, '5 étoiles')])),
                ('note_ponctualite', models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 étoile'), (2, '2 étoiles'), (3, '3 étoiles'), (4, '4 étoiles'), (5, '5 étoiles')], null=True)),
                ('note_competence', models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 étoile'), (2, '2 étoiles'), (3, '3 étoiles'), (4, '4 étoiles'), (5, '5 étoiles')], null=True)),
                ('note_communication', models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 étoile'), (2, '2 étoiles'), (3, '3 étoiles'), (4, '4 étoiles'), (5, '5 étoiles')], null=True)),
                ('note_rapport_qualite_prix', models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 étoile'), (2, '2 étoiles'), (3, '3 étoiles'), (4, '4 étoiles'), (5, '5 étoiles')], null=True)),
                ('commentaire', models.TextField(blank=True)),
                ('points_positifs', models.TextField(blank=True)),
                ('points_amelioration', models.TextField(blank=True)),
                ('recommande', models.BooleanField(default=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('verifie', models.BooleanField(default=False)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('reservation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='avis', to='services.reservationservice')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='avis', to='services.service')),
            ],
            options={
                'verbose_name': 'Avis service',
                'verbose_name_plural': 'Avis services',
                'ordering': ['-date_creation'],
            },
        ),
        migrations.CreateModel(
            name='CreneauDisponibilite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('jour_semaine', models.PositiveSmallIntegerField(choices=[(0, 'Lundi'), (1, 'Mardi'), (2, 'Mercredi'), (3, 'Jeudi'), (4, 'Vendredi'), (5, 'Samedi'), (6, 'Dimanche')])),
                ('heure_debut', models.TimeField()),
                ('heure_fin', models.TimeField()),
                ('actif', models.BooleanField(default=True)),
                ('prestataire', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='creneaux_disponibilite', to=settings.AUTH_USER_MODEL)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='creneaux', to='services.service')),
            ],
            options={
                'verbose_name': 'Créneau disponibilité',
                'verbose_name_plural': 'Créneaux disponibilité',
                'unique_together': {('service', 'prestataire', 'jour_semaine', 'heure_debut')},
            },
        ),
    ]

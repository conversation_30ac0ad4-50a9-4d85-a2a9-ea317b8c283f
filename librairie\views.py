from django.shortcuts import render, get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from .models import LivrePhysique, Collection, Reservation


def liste_livres_physiques(request):
    """Vue pour afficher la liste des livres physiques"""
    livres = LivrePhysique.objects.filter(actif=True).select_related('editeur').prefetch_related('auteurs')

    # Filtrage par format
    format_livre = request.GET.get('format')
    if format_livre:
        livres = livres.filter(format_livre=format_livre)

    # Filtrage par état
    etat = request.GET.get('etat')
    if etat:
        livres = livres.filter(etat=etat)

    # Filtrage par occasion
    occasion = request.GET.get('occasion')
    if occasion == 'true':
        livres = livres.filter(occasion=True)
    elif occasion == 'false':
        livres = livres.filter(occasion=False)

    # Recherche
    search = request.GET.get('search')
    if search:
        livres = livres.filter(
            Q(nom__icontains=search) |
            Q(resume__icontains=search) |
            Q(auteurs__nom__icontains=search) |
            Q(auteurs__prenom__icontains=search)
        ).distinct()

    # Tri
    sort = request.GET.get('sort', 'nom')
    if sort == 'prix_asc':
        livres = livres.order_by('prix')
    elif sort == 'prix_desc':
        livres = livres.order_by('-prix')
    elif sort == 'date':
        livres = livres.order_by('-date_creation')
    else:
        livres = livres.order_by('nom')

    # Pagination
    paginator = Paginator(livres, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistiques pour les filtres
    formats = LivrePhysique.objects.filter(actif=True).values_list('format_livre', flat=True).distinct()
    etats = LivrePhysique.objects.filter(actif=True).values_list('etat', flat=True).distinct()

    context = {
        'livres': page_obj,
        'formats': formats,
        'etats': etats,
        'current_format': format_livre,
        'current_etat': etat,
        'current_occasion': occasion,
        'current_search': search,
        'current_sort': sort,
        'total_livres': livres.count(),
    }

    return render(request, 'librairie/liste.html', context)


def detail_livre_physique(request, livre_id):
    """Vue pour afficher le détail d'un livre physique"""
    livre = get_object_or_404(LivrePhysique, id=livre_id, actif=True)

    # Livres similaires
    livres_similaires = LivrePhysique.objects.filter(
        Q(categorie=livre.categorie) | Q(auteurs__in=livre.auteurs.all()),
        actif=True
    ).exclude(id=livre.id).distinct()[:4]

    # Avis du livre
    avis = livre.avis.filter(verifie=True).order_by('-date_creation')[:5]

    # Vérifier si le livre peut être réservé
    peut_reserver = livre.stock > 0

    context = {
        'livre': livre,
        'livres_similaires': livres_similaires,
        'avis': avis,
        'note_moyenne': livre.note_moyenne,
        'nombre_avis': livre.nombre_avis,
        'peut_reserver': peut_reserver,
    }

    return render(request, 'librairie/detail.html', context)

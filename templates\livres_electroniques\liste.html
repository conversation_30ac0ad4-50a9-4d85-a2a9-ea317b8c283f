{% extends 'base.html' %}

{% block title %}Livres Électroniques - Librairie TAM{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-tablet-alt text-primary"></i>
                Livres Électroniques
            </h1>
            <p class="lead">Découvrez notre collection de livres numériques dans tous les formats : PDF, EPUB, MOBI, AZW.</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-filter"></i> Filtres</h5>
                </div>
                <div class="card-body">
                    <h6>Format</h6>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="pdf">
                        <label class="form-check-label" for="pdf">PDF</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="epub">
                        <label class="form-check-label" for="epub">EPUB</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobi">
                        <label class="form-check-label" for="mobi">MOBI</label>
                    </div>

                    <hr>

                    <h6>Langue</h6>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="fr">
                        <label class="form-check-label" for="fr">Français</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="en">
                        <label class="form-check-label" for="en">Anglais</label>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-9">
            <div class="row mb-3">
                <div class="col-md-6">
                    <form method="get" class="d-flex">
                        <input type="text" name="search" class="form-control" placeholder="Rechercher un livre..." value="{{ current_search }}">
                        {% if current_format %}<input type="hidden" name="format" value="{{ current_format }}">{% endif %}
                        {% if current_langue %}<input type="hidden" name="langue" value="{{ current_langue }}">{% endif %}
                        {% if current_sort %}<input type="hidden" name="sort" value="{{ current_sort }}">{% endif %}
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
                <div class="col-md-6">
                    <form method="get">
                        {% if current_search %}<input type="hidden" name="search" value="{{ current_search }}">{% endif %}
                        {% if current_format %}<input type="hidden" name="format" value="{{ current_format }}">{% endif %}
                        {% if current_langue %}<input type="hidden" name="langue" value="{{ current_langue }}">{% endif %}
                        <select name="sort" class="form-select" onchange="this.form.submit()">
                            <option value="">Trier par...</option>
                            <option value="nom" {% if current_sort == 'nom' %}selected{% endif %}>Titre A-Z</option>
                            <option value="prix_asc" {% if current_sort == 'prix_asc' %}selected{% endif %}>Prix croissant</option>
                            <option value="prix_desc" {% if current_sort == 'prix_desc' %}selected{% endif %}>Prix décroissant</option>
                            <option value="date" {% if current_sort == 'date' %}selected{% endif %}>Plus récents</option>
                        </select>
                    </form>
                </div>
            </div>

            {% if livres %}
                <div class="row">
                    {% for livre in livres %}
                        <div class="col-md-4 mb-4">
                            <div class="card product-card h-100">
                                {% if livre.couverture %}
                                    <img src="{{ livre.couverture.url }}" class="card-img-top product-image" alt="{{ livre.nom }}">
                                {% else %}
                                    <img src="https://via.placeholder.com/300x400?text=Livre+Électronique" class="card-img-top product-image" alt="{{ livre.nom }}">
                                {% endif %}
                                <div class="card-body">
                                    <h6 class="card-title">{{ livre.nom }}</h6>
                                    <p class="text-muted small">
                                        Par {% for auteur in livre.auteurs.all %}{{ auteur.nom_complet }}{% if not forloop.last %}, {% endif %}{% endfor %}
                                    </p>
                                    <div class="mb-2">
                                        <span class="badge bg-primary">{{ livre.get_format_fichier_display }}</span>
                                        <span class="badge bg-secondary">{{ livre.get_langue_display }}</span>
                                    </div>
                                    {% if livre.note_moyenne %}
                                        <div class="rating mb-2">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= livre.note_moyenne %}
                                                    <i class="fas fa-star"></i>
                                                {% else %}
                                                    <i class="far fa-star"></i>
                                                {% endif %}
                                            {% endfor %}
                                            <small class="text-muted">({{ livre.note_moyenne }})</small>
                                        </div>
                                    {% endif %}
                                    <p class="product-price">{{ livre.prix }} €</p>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-primary btn-sm flex-fill" onclick="ajouterAuPanier({{ livre.id }}, 'livres_electroniques')">
                                            <i class="fas fa-download"></i> Acheter
                                        </button>
                                        <a href="{% url 'livres_electroniques:detail' livre.id %}" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if livres.has_other_pages %}
                    <nav aria-label="Navigation des pages">
                        <ul class="pagination justify-content-center">
                            {% if livres.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ livres.previous_page_number }}{% if current_search %}&search={{ current_search }}{% endif %}{% if current_format %}&format={{ current_format }}{% endif %}{% if current_langue %}&langue={{ current_langue }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}">Précédent</a>
                                </li>
                            {% endif %}

                            {% for num in livres.paginator.page_range %}
                                {% if livres.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if current_search %}&search={{ current_search }}{% endif %}{% if current_format %}&format={{ current_format }}{% endif %}{% if current_langue %}&langue={{ current_langue }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if livres.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ livres.next_page_number }}{% if current_search %}&search={{ current_search }}{% endif %}{% if current_format %}&format={{ current_format }}{% endif %}{% if current_langue %}&langue={{ current_langue }}{% endif %}{% if current_sort %}&sort={{ current_sort }}{% endif %}">Suivant</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Aucun livre électronique trouvé.
                    {% if current_search or current_format or current_langue %}
                        <br><a href="{% url 'livres_electroniques:liste' %}" class="alert-link">Voir tous les livres</a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function ajouterAuPanier(produitId, boutiqueType) {
    fetch('{% url "common:ajouter_au_panier" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            'produit_id': produitId,
            'boutique_type': boutiqueType,
            'quantite': 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Afficher un message de succès
            alert(data.message);

            // Mettre à jour le compteur du panier dans la navbar
            const navCounter = document.querySelector('.navbar .badge');
            if (navCounter) {
                navCounter.textContent = data.panier_count;
            }
        } else {
            alert('Erreur: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Une erreur est survenue');
    });
}
</script>
{% endblock %}
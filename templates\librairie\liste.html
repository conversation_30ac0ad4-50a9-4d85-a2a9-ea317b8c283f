{% extends 'base.html' %}

{% block title %}Librairie - Livres Physiques - Librairie TAM{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-book text-success"></i>
                Librairie - Livres Physiques
            </h1>
            <p class="lead">Découvrez notre collection de livres physiques neufs et d'occasion pour tous les goûts.</p>
        </div>
    </div>

    {% if livres %}
        <div class="row">
            {% for livre in livres %}
                <div class="col-md-4 mb-4">
                    <div class="card product-card h-100">
                        {% if livre.couverture %}
                            <img src="{{ livre.couverture.url }}" class="card-img-top product-image" alt="{{ livre.nom }}">
                        {% else %}
                            <img src="https://via.placeholder.com/300x400?text=Livre+Physique" class="card-img-top product-image" alt="{{ livre.nom }}">
                        {% endif %}
                        <div class="card-body">
                            <h6 class="card-title">{{ livre.nom }}</h6>
                            <p class="text-muted small">
                                Par {% for auteur in livre.auteurs.all %}{{ auteur.nom_complet }}{% if not forloop.last %}, {% endif %}{% endfor %}
                            </p>
                            <div class="mb-2">
                                <span class="badge bg-success">{{ livre.get_format_livre_display }}</span>
                                <span class="badge bg-info">{{ livre.get_etat_display }}</span>
                                {% if livre.occasion %}<span class="badge bg-warning">Occasion</span>{% endif %}
                            </div>
                            {% if livre.note_moyenne %}
                                <div class="rating mb-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= livre.note_moyenne %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                    <small class="text-muted">({{ livre.note_moyenne }})</small>
                                </div>
                            {% endif %}
                            <p class="product-price">{{ livre.prix }} €</p>
                            <p class="text-muted small">Stock: {{ livre.stock }}</p>
                            <div class="d-flex gap-2">
                                <button class="btn btn-success btn-sm flex-fill" onclick="ajouterAuPanier({{ livre.id }}, 'librairie')">
                                    <i class="fas fa-shopping-cart"></i> Acheter
                                </button>
                                <a href="{% url 'librairie:detail' livre.id %}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-success">
            <i class="fas fa-info-circle"></i>
            Aucun livre physique trouvé.
            <br>
            <small>Livres neufs, d'occasion, collections spéciales et réservations disponibles.</small>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function ajouterAuPanier(produitId, boutiqueType) {
    fetch('{% url "common:ajouter_au_panier" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            'produit_id': produitId,
            'boutique_type': boutiqueType,
            'quantite': 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            const navCounter = document.querySelector('.navbar .badge');
            if (navCounter) {
                navCounter.textContent = data.panier_count;
            }
        } else {
            alert('Erreur: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Une erreur est survenue');
    });
}
</script>
{% endblock %}
from django.db import models
from django.contrib.auth.models import User

class BaseProduct(models.Model):
    """Modèle de base pour tous les produits"""
    
    nom = models.CharField(max_length=200)
    description = models.TextField()
    prix = models.DecimalField(max_digits=10, decimal_places=2)
    stock = models.PositiveIntegerField(default=0)
    image = models.ImageField(upload_to='products/', blank=True, null=True)
    actif = models.BooleanField(default=True)
    date_creation = models.DateTimeField(auto_now_add=True)
    date_modification = models.DateTimeField(auto_now=True)
    vendeur = models.ForeignKey(User, on_delete=models.CASCADE, related_name='%(class)s_products')
    
    class Meta:
        abstract = True
        ordering = ['-date_creation']
    
    def __str__(self):
        return self.nom

class Categorie(models.Model):
    """Catégories pour organiser les produits"""
    
    nom = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='sous_categories')
    actif = models.BooleanField(default=True)
    
    def __str__(self):
        return self.nom
    
    class Meta:
        verbose_name = "Catégorie"
        verbose_name_plural = "Catégories"

class Commande(models.Model):
    """Modèle pour les commandes"""
    
    STATUS_CHOICES = [
        ('en_attente', 'En attente'),
        ('confirmee', 'Confirmée'),
        ('en_preparation', 'En préparation'),
        ('expediee', 'Expédiée'),
        ('livree', 'Livrée'),
        ('annulee', 'Annulée'),
    ]
    
    client = models.ForeignKey(User, on_delete=models.CASCADE, related_name='commandes')
    numero_commande = models.CharField(max_length=50, unique=True)
    statut = models.CharField(max_length=20, choices=STATUS_CHOICES, default='en_attente')
    total = models.DecimalField(max_digits=10, decimal_places=2)
    date_commande = models.DateTimeField(auto_now_add=True)
    date_livraison_prevue = models.DateTimeField(blank=True, null=True)
    adresse_livraison = models.TextField()
    notes = models.TextField(blank=True)
    
    def __str__(self):
        return f"Commande {self.numero_commande} - {self.client.username}"
    
    class Meta:
        verbose_name = "Commande"
        verbose_name_plural = "Commandes"
        ordering = ['-date_commande']

class LigneCommande(models.Model):
    """Lignes de commande pour détailler les produits commandés"""
    
    commande = models.ForeignKey(Commande, on_delete=models.CASCADE, related_name='lignes')
    produit_nom = models.CharField(max_length=200)  # Nom du produit au moment de la commande
    produit_prix = models.DecimalField(max_digits=10, decimal_places=2)  # Prix au moment de la commande
    quantite = models.PositiveIntegerField()
    sous_total = models.DecimalField(max_digits=10, decimal_places=2)
    
    def save(self, *args, **kwargs):
        self.sous_total = self.produit_prix * self.quantite
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.produit_nom} x {self.quantite}"
    
    class Meta:
        verbose_name = "Ligne de commande"
        verbose_name_plural = "Lignes de commande"

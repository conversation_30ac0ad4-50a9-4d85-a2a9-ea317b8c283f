from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid

class BaseProduct(models.Model):
    """Modèle de base pour tous les produits"""
    
    nom = models.CharField(max_length=200)
    description = models.TextField()
    prix = models.DecimalField(max_digits=10, decimal_places=2)
    stock = models.PositiveIntegerField(default=0)
    image = models.ImageField(upload_to='products/', blank=True, null=True)
    actif = models.BooleanField(default=True)
    date_creation = models.DateTimeField(auto_now_add=True)
    date_modification = models.DateTimeField(auto_now=True)
    vendeur = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='%(class)s_products')
    
    class Meta:
        abstract = True
        ordering = ['-date_creation']
    
    def __str__(self):
        return self.nom

class Categorie(models.Model):
    """Catégories pour organiser les produits"""
    
    nom = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='sous_categories')
    actif = models.BooleanField(default=True)
    
    def __str__(self):
        return self.nom
    
    class Meta:
        verbose_name = "Catégorie"
        verbose_name_plural = "Catégories"

class Commande(models.Model):
    """Modèle pour les commandes"""
    
    STATUS_CHOICES = [
        ('en_attente', 'En attente'),
        ('confirmee', 'Confirmée'),
        ('en_preparation', 'En préparation'),
        ('expediee', 'Expédiée'),
        ('livree', 'Livrée'),
        ('annulee', 'Annulée'),
    ]
    
    client = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='commandes')
    numero_commande = models.CharField(max_length=50, unique=True, default=uuid.uuid4)
    statut = models.CharField(max_length=20, choices=STATUS_CHOICES, default='en_attente')
    total = models.DecimalField(max_digits=10, decimal_places=2)
    date_commande = models.DateTimeField(auto_now_add=True)
    date_livraison_prevue = models.DateTimeField(blank=True, null=True)
    adresse_livraison = models.TextField()
    notes = models.TextField(blank=True)
    
    def __str__(self):
        return f"Commande {self.numero_commande} - {self.client.email}"
    
    class Meta:
        verbose_name = "Commande"
        verbose_name_plural = "Commandes"
        ordering = ['-date_commande']

class LigneCommande(models.Model):
    """Lignes de commande pour détailler les produits commandés"""
    
    commande = models.ForeignKey(Commande, on_delete=models.CASCADE, related_name='lignes')
    produit_nom = models.CharField(max_length=200)  # Nom du produit au moment de la commande
    produit_prix = models.DecimalField(max_digits=10, decimal_places=2)  # Prix au moment de la commande
    quantite = models.PositiveIntegerField()
    sous_total = models.DecimalField(max_digits=10, decimal_places=2)
    
    def save(self, *args, **kwargs):
        self.sous_total = self.produit_prix * self.quantite
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.produit_nom} x {self.quantite}"
    
    class Meta:
        verbose_name = "Ligne de commande"
        verbose_name_plural = "Lignes de commande"


class Boutique(models.Model):
    """Modèle pour les différentes boutiques"""

    TYPES_BOUTIQUE = [
        ('livres_electroniques', 'Livres Électroniques'),
        ('librairie', 'Librairie'),
        ('accessoires_bureautique', 'Accessoires Bureautique'),
        ('accessoires_informatique', 'Accessoires Informatique'),
        ('services', 'Services'),
    ]

    nom = models.CharField(max_length=100)
    type_boutique = models.CharField(max_length=30, choices=TYPES_BOUTIQUE, unique=True)
    description = models.TextField()
    logo = models.ImageField(upload_to='boutiques/', blank=True, null=True)
    banniere = models.ImageField(upload_to='boutiques/bannieres/', blank=True, null=True)
    actif = models.BooleanField(default=True)
    responsable = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    date_creation = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.nom

    class Meta:
        verbose_name = "Boutique"
        verbose_name_plural = "Boutiques"


class Panier(models.Model):
    """Modèle pour le panier d'achat"""

    client = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='panier')
    date_creation = models.DateTimeField(auto_now_add=True)
    date_modification = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Panier de {self.client.email}"

    @property
    def total(self):
        return sum(item.sous_total for item in self.items.all())

    @property
    def nombre_items(self):
        return sum(item.quantite for item in self.items.all())

    class Meta:
        verbose_name = "Panier"
        verbose_name_plural = "Paniers"


class ItemPanier(models.Model):
    """Items dans le panier"""

    panier = models.ForeignKey(Panier, on_delete=models.CASCADE, related_name='items')
    produit_nom = models.CharField(max_length=200)
    produit_prix = models.DecimalField(max_digits=10, decimal_places=2)
    produit_id = models.PositiveIntegerField()  # ID du produit dans sa table spécifique
    boutique_type = models.CharField(max_length=30)  # Type de boutique pour retrouver le produit
    quantite = models.PositiveIntegerField(default=1)
    date_ajout = models.DateTimeField(auto_now_add=True)

    @property
    def sous_total(self):
        return self.produit_prix * self.quantite

    def __str__(self):
        return f"{self.produit_nom} x {self.quantite}"

    class Meta:
        verbose_name = "Item panier"
        verbose_name_plural = "Items panier"
        unique_together = ['panier', 'produit_id', 'boutique_type']

{% extends 'base.html' %}

{% block title %}Services - Librairie TAM{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-tools text-danger"></i>
                Services
            </h1>
            <p class="lead">Services personnalisés : formations, réparations, consultations, installations et maintenance.</p>
        </div>
    </div>

    {% if services %}
        <div class="row">
            {% for service in services %}
                <div class="col-md-4 mb-4">
                    <div class="card product-card h-100">
                        <div class="card-body">
                            <h6 class="card-title">{{ service.nom }}</h6>
                            <div class="mb-2">
                                <span class="badge bg-danger">{{ service.get_type_service_display }}</span>
                                <span class="badge bg-secondary">{{ service.duree_estimee }}</span>
                                {% if service.lieu_prestation %}<span class="badge bg-info">{{ service.get_lieu_prestation_display }}</span>{% endif %}
                            </div>
                            {% if service.note_moyenne %}
                                <div class="rating mb-2">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= service.note_moyenne %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                    <small class="text-muted">({{ service.note_moyenne }})</small>
                                </div>
                            {% endif %}
                            <p class="text-muted">{{ service.description|truncatewords:20 }}</p>
                            <p class="product-price">{{ service.prix }} €</p>
                            <div class="mb-2">
                                {% if service.disponible_weekend %}<span class="badge bg-success">Weekend</span>{% endif %}
                                {% if service.disponible_soir %}<span class="badge bg-warning">Soir</span>{% endif %}
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-danger btn-sm flex-fill" onclick="ajouterAuPanier({{ service.id }}, 'services')">
                                    <i class="fas fa-calendar-plus"></i> Réserver
                                </button>
                                <a href="{% url 'services:detail' service.id %}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-danger">
            <i class="fas fa-info-circle"></i>
            Aucun service trouvé.
            <br>
            <small>Formations, réparations, consultations, installations et services de maintenance.</small>
        </div>
    {% endif %}

{% block extra_js %}
<script>
function ajouterAuPanier(produitId, boutiqueType) {
    fetch('{% url "common:ajouter_au_panier" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            'produit_id': produitId,
            'boutique_type': boutiqueType,
            'quantite': 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            const navCounter = document.querySelector('.navbar .badge');
            if (navCounter) {
                navCounter.textContent = data.panier_count;
            }
        } else {
            alert('Erreur: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Une erreur est survenue');
    });
}
</script>
{% endblock %}
</div>
{% endblock %}
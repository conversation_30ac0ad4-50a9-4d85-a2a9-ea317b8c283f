from django.shortcuts import render, get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from .models import Service, ReservationService, CreneauDisponibilite


def liste_services(request):
    """Vue pour afficher la liste des services"""
    services = Service.objects.filter(actif=True)

    # Filtrage par type
    type_service = request.GET.get('type')
    if type_service:
        services = services.filter(type_service=type_service)

    # Filtrage par lieu de prestation
    lieu_prestation = request.GET.get('lieu')
    if lieu_prestation:
        services = services.filter(lieu_prestation=lieu_prestation)

    # Filtrage par disponibilité weekend
    weekend = request.GET.get('weekend')
    if weekend == 'true':
        services = services.filter(disponible_weekend=True)

    # Filtrage par disponibilité soir
    soir = request.GET.get('soir')
    if soir == 'true':
        services = services.filter(disponible_soir=True)

    # Recherche
    search = request.GET.get('search')
    if search:
        services = services.filter(
            Q(nom__icontains=search) |
            Q(description__icontains=search) |
            Q(deroulement__icontains=search)
        ).distinct()

    # Tri
    sort = request.GET.get('sort', 'nom')
    if sort == 'prix_asc':
        services = services.order_by('prix')
    elif sort == 'prix_desc':
        services = services.order_by('-prix')
    elif sort == 'duree':
        services = services.order_by('duree_estimee')
    elif sort == 'date':
        services = services.order_by('-date_creation')
    else:
        services = services.order_by('nom')

    # Pagination
    paginator = Paginator(services, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Données pour les filtres
    types = Service.objects.filter(actif=True).values_list('type_service', flat=True).distinct()
    lieux = Service.objects.filter(actif=True).values_list('lieu_prestation', flat=True).distinct()

    context = {
        'services': page_obj,
        'types': types,
        'lieux': lieux,
        'current_type': type_service,
        'current_lieu': lieu_prestation,
        'current_weekend': weekend,
        'current_soir': soir,
        'current_search': search,
        'current_sort': sort,
        'total_services': services.count(),
    }

    return render(request, 'services/liste.html', context)


def detail_service(request, service_id):
    """Vue pour afficher le détail d'un service"""
    service = get_object_or_404(Service, id=service_id, actif=True)

    # Services similaires
    services_similaires = Service.objects.filter(
        type_service=service.type_service,
        actif=True
    ).exclude(id=service.id)[:4]

    # Avis du service
    avis = service.avis.filter(verifie=True).order_by('-date_creation')[:5]

    # Créneaux de disponibilité
    creneaux = CreneauDisponibilite.objects.filter(service=service, actif=True)

    context = {
        'service': service,
        'services_similaires': services_similaires,
        'avis': avis,
        'note_moyenne': service.note_moyenne,
        'nombre_avis': service.nombre_avis,
        'nombre_prestations': service.nombre_prestations,
        'creneaux': creneaux,
    }

    return render(request, 'services/detail.html', context)

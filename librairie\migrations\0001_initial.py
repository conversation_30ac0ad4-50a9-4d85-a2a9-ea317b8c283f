# Generated by Django 5.1.5 on 2025-08-06 22:21

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Collection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='collections/')),
            ],
            options={
                'verbose_name': 'Collection',
                'verbose_name_plural': 'Collections',
            },
        ),
        migrations.CreateModel(
            name='LivrePhysique',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('prix', models.DecimalField(decimal_places=2, max_digits=10)),
                ('stock', models.PositiveIntegerField(default=0)),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/')),
                ('actif', models.BooleanField(default=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('date_modification', models.DateTimeField(auto_now=True)),
                ('isbn', models.CharField(blank=True, max_length=13, null=True, unique=True)),
                ('format_livre', models.CharField(choices=[('poche', 'Livre de poche'), ('broche', 'Broché'), ('relie', 'Relié'), ('grand_format', 'Grand format')], default='broche', max_length=20)),
                ('nombre_pages', models.PositiveIntegerField(blank=True, null=True)),
                ('dimensions', models.CharField(blank=True, help_text='Ex: 15 x 23 cm', max_length=50)),
                ('poids', models.CharField(blank=True, help_text='Ex: 350g', max_length=20)),
                ('etat', models.CharField(choices=[('neuf', 'Neuf'), ('tres_bon', 'Très bon état'), ('bon', 'Bon état'), ('correct', 'État correct')], default='neuf', max_length=20)),
                ('occasion', models.BooleanField(default=False)),
                ('resume', models.TextField(help_text='Résumé du livre')),
                ('table_matieres', models.TextField(blank=True)),
                ('extrait', models.TextField(blank=True, help_text='Extrait du livre')),
                ('couverture', models.ImageField(blank=True, null=True, upload_to='livres/couvertures/')),
                ('image_dos', models.ImageField(blank=True, null=True, upload_to='livres/dos/')),
                ('date_publication', models.DateField(blank=True, null=True)),
                ('edition', models.CharField(blank=True, max_length=50)),
                ('langue', models.CharField(default='Français', max_length=50)),
                ('note_moyenne', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('nombre_avis', models.PositiveIntegerField(default=0)),
            ],
            options={
                'verbose_name': 'Livre physique',
                'verbose_name_plural': 'Livres physiques',
            },
        ),
        migrations.CreateModel(
            name='Reservation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('statut', models.CharField(choices=[('active', 'Active'), ('prete', 'Prête à retirer'), ('retiree', 'Retirée'), ('annulee', 'Annulée'), ('expiree', 'Expirée')], default='active', max_length=20)),
                ('date_reservation', models.DateTimeField(auto_now_add=True)),
                ('date_expiration', models.DateTimeField()),
                ('notes', models.TextField(blank=True)),
            ],
            options={
                'verbose_name': 'Réservation',
                'verbose_name_plural': 'Réservations',
                'ordering': ['-date_reservation'],
            },
        ),
        migrations.CreateModel(
            name='AvisLivrePhysique',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.PositiveSmallIntegerField(choices=[(1, '1 étoile'), (2, '2 étoiles'), (3, '3 étoiles'), (4, '4 étoiles'), (5, '5 étoiles')])),
                ('commentaire', models.TextField(blank=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('verifie', models.BooleanField(default=False)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Avis livre physique',
                'verbose_name_plural': 'Avis livres physiques',
                'ordering': ['-date_creation'],
            },
        ),
    ]

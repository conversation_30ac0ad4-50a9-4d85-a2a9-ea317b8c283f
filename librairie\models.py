from django.db import models
from django.conf import settings
from common.models import BaseProduct, Categorie
from livres_electroniques.models import Auteur, Editeur


class LivrePhysique(BaseProduct):
    """Modèle pour les livres physiques"""

    FORMAT_CHOICES = [
        ('poche', 'Livre de poche'),
        ('broche', 'Broché'),
        ('relie', 'Reli<PERSON>'),
        ('grand_format', 'Grand format'),
    ]

    ETAT_CHOICES = [
        ('neuf', 'Neuf'),
        ('tres_bon', 'Très bon état'),
        ('bon', 'Bon état'),
        ('correct', 'État correct'),
    ]

    # Informations bibliographiques
    isbn = models.CharField(max_length=13, unique=True, blank=True, null=True)
    auteurs = models.ManyToManyField(Auteur, related_name='livres_physiques')
    editeur = models.ForeignKey(Editeur, on_delete=models.SET_NULL, null=True, blank=True)
    categorie = models.ForeignKey(Categorie, on_delete=models.SET_NULL, null=True, blank=True)

    # Caractéristiques physiques
    format_livre = models.CharField(max_length=20, choices=FORMAT_CHOICES, default='broche')
    nombre_pages = models.PositiveIntegerField(blank=True, null=True)
    dimensions = models.CharField(max_length=50, blank=True, help_text='Ex: 15 x 23 cm')
    poids = models.CharField(max_length=20, blank=True, help_text='Ex: 350g')

    # État et condition
    etat = models.CharField(max_length=20, choices=ETAT_CHOICES, default='neuf')
    occasion = models.BooleanField(default=False)

    # Contenu
    resume = models.TextField(help_text='Résumé du livre')
    table_matieres = models.TextField(blank=True)
    extrait = models.TextField(blank=True, help_text='Extrait du livre')

    # Images
    couverture = models.ImageField(upload_to='livres/couvertures/', blank=True, null=True)
    image_dos = models.ImageField(upload_to='livres/dos/', blank=True, null=True)

    # Métadonnées
    date_publication = models.DateField(blank=True, null=True)
    edition = models.CharField(max_length=50, blank=True)
    langue = models.CharField(max_length=50, default='Français')

    # Statistiques
    note_moyenne = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    nombre_avis = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"{self.nom} - {self.get_format_livre_display()}"

    @property
    def auteurs_list(self):
        return ", ".join([str(auteur) for auteur in self.auteurs.all()])

    @property
    def disponible(self):
        return self.stock > 0

    class Meta:
        verbose_name = "Livre physique"
        verbose_name_plural = "Livres physiques"


class AvisLivrePhysique(models.Model):
    """Avis sur les livres physiques"""

    NOTES_CHOICES = [
        (1, '1 étoile'),
        (2, '2 étoiles'),
        (3, '3 étoiles'),
        (4, '4 étoiles'),
        (5, '5 étoiles'),
    ]

    livre = models.ForeignKey(LivrePhysique, on_delete=models.CASCADE, related_name='avis')
    client = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    note = models.PositiveSmallIntegerField(choices=NOTES_CHOICES)
    commentaire = models.TextField(blank=True)
    date_creation = models.DateTimeField(auto_now_add=True)
    verifie = models.BooleanField(default=False)

    def __str__(self):
        return f"Avis de {self.client.email} sur {self.livre.nom} - {self.note}/5"

    class Meta:
        verbose_name = "Avis livre physique"
        verbose_name_plural = "Avis livres physiques"
        unique_together = ['livre', 'client']
        ordering = ['-date_creation']


class Collection(models.Model):
    """Collections de livres"""

    nom = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    editeur = models.ForeignKey(Editeur, on_delete=models.CASCADE, related_name='collections')
    image = models.ImageField(upload_to='collections/', blank=True, null=True)

    def __str__(self):
        return f"{self.nom} ({self.editeur.nom})"

    class Meta:
        verbose_name = "Collection"
        verbose_name_plural = "Collections"


class Reservation(models.Model):
    """Réservations de livres"""

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('prete', 'Prête à retirer'),
        ('retiree', 'Retirée'),
        ('annulee', 'Annulée'),
        ('expiree', 'Expirée'),
    ]

    livre = models.ForeignKey(LivrePhysique, on_delete=models.CASCADE, related_name='reservations')
    client = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='reservations')
    statut = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    date_reservation = models.DateTimeField(auto_now_add=True)
    date_expiration = models.DateTimeField()
    notes = models.TextField(blank=True)

    def __str__(self):
        return f"Réservation de {self.livre.nom} par {self.client.email}"

    class Meta:
        verbose_name = "Réservation"
        verbose_name_plural = "Réservations"
        unique_together = ['livre', 'client']
        ordering = ['-date_reservation']

{% load static %}
{% load django_bootstrap5 %}
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Librairie TAM - Votre librairie multiservices{% endblock %}</title>

    <!-- Bootstrap CSS -->
    {% bootstrap_css %}

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">
                <i class="fas fa-book-open"></i> Librairie TAM
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="boutiquesDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-store"></i> Boutiques
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'livres_electroniques:livre_list' %}">
                                <i class="fas fa-tablet-alt"></i> Livres Électroniques
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'librairie:livre_list' %}">
                                <i class="fas fa-book"></i> Librairie
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'accessoires_bureautique:accessoire_list' %}">
                                <i class="fas fa-pen"></i> Accessoires Bureautique
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'accessoires_informatique:accessoire_list' %}">
                                <i class="fas fa-computer"></i> Accessoires Informatique
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'services:service_list' %}">
                                <i class="fas fa-tools"></i> Services
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'blog:article_list' %}">
                            <i class="fas fa-newspaper"></i> Blog
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ user.first_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'accounts:dashboard' %}">
                                    <i class="fas fa-tachometer-alt"></i> Tableau de bord
                                </a></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                    <i class="fas fa-user-edit"></i> Mon profil
                                </a></li>
                                {% if user.role == 'client' %}
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-shopping-cart"></i> Mon panier
                                </a></li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">
                                    <i class="fas fa-sign-out-alt"></i> Déconnexion
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:login' %}">
                                <i class="fas fa-sign-in-alt"></i> Connexion
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:signup' %}">
                                <i class="fas fa-user-plus"></i> Inscription
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="container mt-3">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-book-open"></i> Librairie TAM</h5>
                    <p>Votre librairie multiservices pour tous vos besoins en livres, accessoires et services.</p>
                </div>
                <div class="col-md-4">
                    <h5>Nos Boutiques</h5>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'livres_electroniques:livre_list' %}" class="text-light">Livres Électroniques</a></li>
                        <li><a href="{% url 'librairie:livre_list' %}" class="text-light">Librairie</a></li>
                        <li><a href="{% url 'accessoires_bureautique:accessoire_list' %}" class="text-light">Accessoires Bureautique</a></li>
                        <li><a href="{% url 'accessoires_informatique:accessoire_list' %}" class="text-light">Accessoires Informatique</a></li>
                        <li><a href="{% url 'services:service_list' %}" class="text-light">Services</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Contact</h5>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +33 1 23 45 67 89</p>
                    <p><i class="fas fa-map-marker-alt"></i> 123 Rue de la Librairie, 75001 Paris</p>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; 2025 Librairie TAM. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    {% bootstrap_javascript %}

    <!-- Custom JS -->
    <script src="{% static 'js/script.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
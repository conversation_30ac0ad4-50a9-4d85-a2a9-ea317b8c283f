from django.shortcuts import render, get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from .models import AccessoireInformatique, MarqueInformatique, GuideCompatibilite


def liste_accessoires_informatique(request):
    """Vue pour afficher la liste des accessoires informatique"""
    accessoires = AccessoireInformatique.objects.filter(actif=True).select_related('marque', 'categorie')

    # Filtrage par type
    type_accessoire = request.GET.get('type')
    if type_accessoire:
        accessoires = accessoires.filter(type_accessoire=type_accessoire)

    # Filtrage par marque
    marque_id = request.GET.get('marque')
    if marque_id:
        accessoires = accessoires.filter(marque_id=marque_id)

    # Filtrage par compatibilité
    compatibilite = request.GET.get('compatibilite')
    if compatibilite:
        accessoires = accessoires.filter(compatibilite__icontains=compatibilite)

    # Recherche
    search = request.GET.get('search')
    if search:
        accessoires = accessoires.filter(
            Q(nom__icontains=search) |
            Q(description__icontains=search) |
            Q(marque__nom__icontains=search) |
            Q(modele__icontains=search)
        ).distinct()

    # Tri
    sort = request.GET.get('sort', 'nom')
    if sort == 'prix_asc':
        accessoires = accessoires.order_by('prix')
    elif sort == 'prix_desc':
        accessoires = accessoires.order_by('-prix')
    elif sort == 'date':
        accessoires = accessoires.order_by('-date_creation')
    else:
        accessoires = accessoires.order_by('nom')

    # Pagination
    paginator = Paginator(accessoires, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Données pour les filtres
    types = AccessoireInformatique.objects.filter(actif=True).values_list('type_accessoire', flat=True).distinct()
    marques = MarqueInformatique.objects.all()
    compatibilites = ['Windows', 'Mac', 'Linux', 'Android', 'iOS']

    context = {
        'accessoires': page_obj,
        'types': types,
        'marques': marques,
        'compatibilites': compatibilites,
        'current_type': type_accessoire,
        'current_marque': marque_id,
        'current_compatibilite': compatibilite,
        'current_search': search,
        'current_sort': sort,
        'total_accessoires': accessoires.count(),
    }

    return render(request, 'accessoires_informatique/liste.html', context)


def detail_accessoire_informatique(request, accessoire_id):
    """Vue pour afficher le détail d'un accessoire informatique"""
    accessoire = get_object_or_404(AccessoireInformatique, id=accessoire_id, actif=True)

    # Accessoires similaires
    accessoires_similaires = AccessoireInformatique.objects.filter(
        Q(type_accessoire=accessoire.type_accessoire) | Q(marque=accessoire.marque),
        actif=True
    ).exclude(id=accessoire.id)[:4]

    # Avis de l'accessoire
    avis = accessoire.avis.filter(verifie=True).order_by('-date_creation')[:5]

    # Guide de compatibilité
    guides_compatibilite = GuideCompatibilite.objects.filter(accessoire=accessoire)

    context = {
        'accessoire': accessoire,
        'accessoires_similaires': accessoires_similaires,
        'avis': avis,
        'note_moyenne': accessoire.note_moyenne,
        'nombre_avis': accessoire.nombre_avis,
        'guides_compatibilite': guides_compatibilite,
    }

    return render(request, 'accessoires_informatique/detail.html', context)

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from django.apps import apps
from .models import <PERSON><PERSON>, Item<PERSON><PERSON>er, Commande, LigneCommande
import json


@login_required
def voir_panier(request):
    """Vue pour afficher le panier de l'utilisateur"""
    panier, created = Panier.objects.get_or_create(client=request.user)
    context = {
        'panier': panier,
        'items': panier.items.all(),
        'total': panier.total,
        'nombre_items': panier.nombre_items,
    }
    return render(request, 'common/panier.html', context)


@login_required
@require_POST
def ajouter_au_panier(request):
    """Vue AJAX pour ajouter un produit au panier"""
    try:
        data = json.loads(request.body)
        produit_id = data.get('produit_id')
        boutique_type = data.get('boutique_type')
        quantite = int(data.get('quantite', 1))

        # Récupérer le modèle de produit selon le type de boutique
        model_mapping = {
            'livres_electroniques': 'livres_electroniques.LivreElectronique',
            'librairie': 'librairie.LivrePhysique',
            'accessoires_bureautique': 'accessoires_bureautique.AccessoireBureautique',
            'accessoires_informatique': 'accessoires_informatique.AccessoireInformatique',
            'services': 'services.Service',
        }

        if boutique_type not in model_mapping:
            return JsonResponse({'success': False, 'error': 'Type de boutique invalide'})

        # Obtenir le modèle et le produit
        model_path = model_mapping[boutique_type]
        app_label, model_name = model_path.split('.')
        model = apps.get_model(app_label, model_name)
        produit = get_object_or_404(model, id=produit_id)

        # Créer ou récupérer le panier
        panier, created = Panier.objects.get_or_create(client=request.user)

        # Ajouter ou mettre à jour l'item dans le panier
        item, item_created = ItemPanier.objects.get_or_create(
            panier=panier,
            produit_id=produit_id,
            boutique_type=boutique_type,
            defaults={
                'produit_nom': produit.nom,
                'produit_prix': produit.prix,
                'quantite': quantite
            }
        )

        if not item_created:
            item.quantite += quantite
            item.save()

        return JsonResponse({
            'success': True,
            'message': f'{produit.nom} ajouté au panier',
            'panier_count': panier.nombre_items,
            'panier_total': float(panier.total)
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_POST
def modifier_quantite_panier(request):
    """Vue AJAX pour modifier la quantité d'un item dans le panier"""
    try:
        data = json.loads(request.body)
        item_id = data.get('item_id')
        nouvelle_quantite = int(data.get('quantite'))

        item = get_object_or_404(ItemPanier, id=item_id, panier__client=request.user)

        if nouvelle_quantite <= 0:
            item.delete()
            message = 'Produit retiré du panier'
        else:
            item.quantite = nouvelle_quantite
            item.save()
            message = 'Quantité mise à jour'

        panier = item.panier
        return JsonResponse({
            'success': True,
            'message': message,
            'panier_count': panier.nombre_items,
            'panier_total': float(panier.total),
            'item_total': float(item.sous_total) if nouvelle_quantite > 0 else 0
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def supprimer_du_panier(request, item_id):
    """Vue pour supprimer un item du panier"""
    item = get_object_or_404(ItemPanier, id=item_id, panier__client=request.user)
    nom_produit = item.produit_nom
    item.delete()
    messages.success(request, f'{nom_produit} retiré du panier')
    return redirect('common:voir_panier')


@login_required
def vider_panier(request):
    """Vue pour vider complètement le panier"""
    panier = get_object_or_404(Panier, client=request.user)
    panier.items.all().delete()
    messages.success(request, 'Panier vidé')
    return redirect('common:voir_panier')


@login_required
def checkout(request):
    """Vue pour le processus de commande"""
    panier = get_object_or_404(Panier, client=request.user)

    if not panier.items.exists():
        messages.error(request, 'Votre panier est vide')
        return redirect('common:voir_panier')

    if request.method == 'POST':
        # Créer la commande
        commande = Commande.objects.create(
            client=request.user,
            total=panier.total,
            statut='en_attente'
        )

        # Créer les lignes de commande
        for item in panier.items.all():
            LigneCommande.objects.create(
                commande=commande,
                produit_nom=item.produit_nom,
                produit_prix=item.produit_prix,
                quantite=item.quantite,
                sous_total=item.sous_total
            )

        # Vider le panier
        panier.items.all().delete()

        messages.success(request, f'Commande #{commande.numero_commande} créée avec succès')
        return redirect('common:commande_detail', commande_id=commande.id)

    context = {
        'panier': panier,
        'items': panier.items.all(),
        'total': panier.total,
    }
    return render(request, 'common/checkout.html', context)


@login_required
def mes_commandes(request):
    """Vue pour afficher les commandes de l'utilisateur"""
    commandes = Commande.objects.filter(client=request.user).order_by('-date_creation')
    return render(request, 'common/mes_commandes.html', {'commandes': commandes})


@login_required
def commande_detail(request, commande_id):
    """Vue pour afficher le détail d'une commande"""
    commande = get_object_or_404(Commande, id=commande_id, client=request.user)
    return render(request, 'common/commande_detail.html', {'commande': commande})
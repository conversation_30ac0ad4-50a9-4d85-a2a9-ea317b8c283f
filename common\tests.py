from django.test import TestCase
from django.contrib.auth import get_user_model
from decimal import Decimal
from .models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, LigneCommande

User = get_user_model()


class CategorieModelTest(TestCase):
    """Tests pour le modèle Categorie"""

    def test_create_categorie(self):
        """Test de création d'une catégorie"""
        categorie = Categorie.objects.create(
            nom='Test Catégorie',
            description='Description de test'
        )
        self.assertEqual(categorie.nom, 'Test Catégorie')
        self.assertEqual(str(categorie), 'Test Catégorie')


class BoutiqueModelTest(TestCase):
    """Tests pour le modèle Boutique"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='responsable',
            email='<EMAIL>',
            password='testpass123',
            role='admin'
        )

    def test_create_boutique(self):
        """Test de création d'une boutique"""
        boutique = Boutique.objects.create(
            nom='Livres Électroniques',
            type_boutique='livres_electroniques',
            description='Boutique de livres numériques',
            responsable=self.user
        )
        self.assertEqual(boutique.nom, 'Livres Électroniques')
        self.assertEqual(boutique.type_boutique, 'livres_electroniques')
        self.assertTrue(boutique.actif)
        self.assertEqual(str(boutique), 'Livres Électroniques')


class PanierModelTest(TestCase):
    """Tests pour le modèle Panier"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='client',
            email='<EMAIL>',
            password='testpass123'
        )
        self.panier = Panier.objects.create(client=self.user)

    def test_panier_empty(self):
        """Test d'un panier vide"""
        self.assertEqual(self.panier.total, 0)
        self.assertEqual(self.panier.nombre_items, 0)
        self.assertEqual(str(self.panier), f'Panier de {self.user.email}')

    def test_panier_with_items(self):
        """Test d'un panier avec des articles"""
        # Ajouter un item au panier
        item = ItemPanier.objects.create(
            panier=self.panier,
            produit_nom='Livre Test',
            produit_prix=Decimal('19.99'),
            produit_id=1,
            boutique_type='livres_electroniques',
            quantite=2
        )

        # Vérifier les calculs
        self.assertEqual(item.sous_total, Decimal('39.98'))
        self.assertEqual(self.panier.total, Decimal('39.98'))
        self.assertEqual(self.panier.nombre_items, 2)
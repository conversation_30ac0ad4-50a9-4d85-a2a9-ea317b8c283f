from django.shortcuts import render, get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from .models import AccessoireBureautique, Marque, PromotionBureautique


def liste_accessoires_bureautique(request):
    """Vue pour afficher la liste des accessoires bureautique"""
    accessoires = AccessoireBureautique.objects.filter(actif=True).select_related('marque', 'categorie')

    # Filtrage par type
    type_accessoire = request.GET.get('type')
    if type_accessoire:
        accessoires = accessoires.filter(type_accessoire=type_accessoire)

    # Filtrage par marque
    marque_id = request.GET.get('marque')
    if marque_id:
        accessoires = accessoires.filter(marque_id=marque_id)

    # Filtrage par couleur
    couleur = request.GET.get('couleur')
    if couleur:
        accessoires = accessoires.filter(couleur=couleur)

    # Recherche
    search = request.GET.get('search')
    if search:
        accessoires = accessoires.filter(
            Q(nom__icontains=search) |
            Q(description__icontains=search) |
            Q(marque__nom__icontains=search)
        ).distinct()

    # Tri
    sort = request.GET.get('sort', 'nom')
    if sort == 'prix_asc':
        accessoires = accessoires.order_by('prix')
    elif sort == 'prix_desc':
        accessoires = accessoires.order_by('-prix')
    elif sort == 'date':
        accessoires = accessoires.order_by('-date_creation')
    else:
        accessoires = accessoires.order_by('nom')

    # Pagination
    paginator = Paginator(accessoires, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Données pour les filtres
    types = AccessoireBureautique.objects.filter(actif=True).values_list('type_accessoire', flat=True).distinct()
    marques = Marque.objects.all()
    couleurs = AccessoireBureautique.objects.filter(actif=True).values_list('couleur', flat=True).distinct()

    context = {
        'accessoires': page_obj,
        'types': types,
        'marques': marques,
        'couleurs': couleurs,
        'current_type': type_accessoire,
        'current_marque': marque_id,
        'current_couleur': couleur,
        'current_search': search,
        'current_sort': sort,
        'total_accessoires': accessoires.count(),
    }

    return render(request, 'accessoires_bureautique/liste.html', context)


def detail_accessoire_bureautique(request, accessoire_id):
    """Vue pour afficher le détail d'un accessoire bureautique"""
    accessoire = get_object_or_404(AccessoireBureautique, id=accessoire_id, actif=True)

    # Accessoires similaires
    accessoires_similaires = AccessoireBureautique.objects.filter(
        Q(type_accessoire=accessoire.type_accessoire) | Q(marque=accessoire.marque),
        actif=True
    ).exclude(id=accessoire.id)[:4]

    # Avis de l'accessoire
    avis = accessoire.avis.filter(verifie=True).order_by('-date_creation')[:5]

    # Promotions actives
    promotions = PromotionBureautique.objects.filter(
        accessoires=accessoire,
        actif=True,
        date_debut__lte=timezone.now(),
        date_fin__gte=timezone.now()
    )

    context = {
        'accessoire': accessoire,
        'accessoires_similaires': accessoires_similaires,
        'avis': avis,
        'note_moyenne': accessoire.note_moyenne,
        'nombre_avis': accessoire.nombre_avis,
        'promotions': promotions,
    }

    return render(request, 'accessoires_bureautique/detail.html', context)

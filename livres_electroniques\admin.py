from django.contrib import admin
from django.utils.html import format_html
from .models import <PERSON><PERSON><PERSON>, <PERSON>eur, LivreElectronique, AvisLivre, TelechargementLivre


@admin.register(Auteur)
class AuteurAdmin(admin.ModelAdmin):
    list_display = ('nom_complet', 'nationalite', 'date_naissance', 'nombre_livres')
    list_filter = ('nationalite', 'date_naissance')
    search_fields = ('nom', 'prenom', 'biographie')

    def nombre_livres(self, obj):
        return obj.livres_electroniques.count()
    nombre_livres.short_description = 'Nombre de livres'


@admin.register(Editeur)
class EditeurAdmin(admin.ModelAdmin):
    list_display = ('nom', 'site_web', 'nombre_livres')
    search_fields = ('nom', 'description')

    def nombre_livres(self, obj):
        return obj.livres_electroniques.count()
    nombre_livres.short_description = 'Nombre de livres'


@admin.register(LivreElectronique)
class LivreElectroniqueAdmin(admin.ModelAdmin):
    list_display = ('nom', 'auteurs_display', 'editeur', 'format_fichier', 'prix', 'stock', 'actif', 'note_moyenne')
    list_filter = ('format_fichier', 'langue', 'actif', 'editeur', 'categorie', 'date_creation')
    search_fields = ('nom', 'isbn', 'resume', 'auteurs__nom', 'auteurs__prenom')
    list_editable = ('prix', 'stock', 'actif')
    filter_horizontal = ('auteurs',)
    readonly_fields = ('note_moyenne', 'nombre_avis', 'nombre_telechargements', 'date_creation', 'date_modification')

    fieldsets = (
        ('Informations principales', {
            'fields': ('nom', 'isbn', 'auteurs', 'editeur', 'categorie')
        }),
        ('Détails du livre', {
            'fields': ('format_fichier', 'taille_fichier', 'nombre_pages', 'langue', 'date_publication')
        }),
        ('Contenu', {
            'fields': ('resume', 'table_matieres', 'couverture', 'fichier_livre', 'extrait_gratuit')
        }),
        ('Commerce', {
            'fields': ('prix', 'stock', 'actif', 'vendeur')
        }),
        ('Statistiques', {
            'fields': ('note_moyenne', 'nombre_avis', 'nombre_telechargements', 'date_creation', 'date_modification'),
            'classes': ('collapse',)
        }),
    )

    def auteurs_display(self, obj):
        return obj.auteurs_list
    auteurs_display.short_description = 'Auteurs'

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser or request.user.role == 'admin':
            return qs
        elif request.user.role == 'vendeur':
            return qs.filter(vendeur=request.user)
        return qs.none()


@admin.register(AvisLivre)
class AvisLivreAdmin(admin.ModelAdmin):
    list_display = ('livre', 'client', 'note', 'verifie', 'date_creation')
    list_filter = ('note', 'verifie', 'date_creation', 'livre__format_fichier')
    search_fields = ('livre__nom', 'client__email', 'commentaire')
    list_editable = ('verifie',)
    readonly_fields = ('date_creation',)


@admin.register(TelechargementLivre)
class TelechargementLivreAdmin(admin.ModelAdmin):
    list_display = ('livre', 'client', 'date_telechargement', 'adresse_ip')
    list_filter = ('date_telechargement', 'livre__format_fichier')
    search_fields = ('livre__nom', 'client__email')
    readonly_fields = ('date_telechargement',)

    def has_add_permission(self, request):
        return False  # Empêcher l'ajout manuel

    def has_change_permission(self, request, obj=None):
        return False  # Empêcher la modification

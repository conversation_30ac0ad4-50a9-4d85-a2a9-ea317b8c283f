# Generated by Django 5.1.5 on 2025-08-06 22:21

import ckeditor.fields
import django.db.models.deletion
import taggit.managers
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('taggit', '0006_rename_taggeditem_content_type_object_id_taggit_tagg_content_8fc721_idx'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CategorieArticle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(blank=True, max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('couleur', models.CharField(default='#007bff', help_text='Code couleur hexadécimal', max_length=7)),
                ('actif', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': "Catégorie d'article",
                'verbose_name_plural': "Catégories d'articles",
            },
        ),
        migrations.CreateModel(
            name='Newsletter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('nom', models.CharField(blank=True, max_length=100)),
                ('actif', models.BooleanField(default=True)),
                ('date_inscription', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Abonnement newsletter',
                'verbose_name_plural': 'Abonnements newsletter',
            },
        ),
        migrations.CreateModel(
            name='Article',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titre', models.CharField(max_length=200)),
                ('slug', models.SlugField(blank=True, max_length=200, unique=True)),
                ('contenu', ckeditor.fields.RichTextField()),
                ('extrait', models.TextField(help_text="Résumé de l'article (300 caractères max)", max_length=300)),
                ('image_principale', models.ImageField(blank=True, null=True, upload_to='blog/articles/')),
                ('statut', models.CharField(choices=[('brouillon', 'Brouillon'), ('publie', 'Publié'), ('archive', 'Archivé')], default='brouillon', max_length=20)),
                ('featured', models.BooleanField(default=False, verbose_name='Article à la une')),
                ('meta_description', models.CharField(blank=True, help_text='Description pour les moteurs de recherche', max_length=160)),
                ('meta_keywords', models.CharField(blank=True, help_text='Mots-clés séparés par des virgules', max_length=255)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('date_modification', models.DateTimeField(auto_now=True)),
                ('date_publication', models.DateTimeField(blank=True, null=True)),
                ('vues', models.PositiveIntegerField(default=0)),
                ('auteur', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='articles', to=settings.AUTH_USER_MODEL)),
                ('tags', taggit.managers.TaggableManager(blank=True, help_text='A comma-separated list of tags.', through='taggit.TaggedItem', to='taggit.Tag', verbose_name='Tags')),
                ('categorie', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='blog.categoriearticle')),
            ],
            options={
                'verbose_name': 'Article',
                'verbose_name_plural': 'Articles',
                'ordering': ['-date_publication', '-date_creation'],
            },
        ),
        migrations.CreateModel(
            name='Commentaire',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contenu', models.TextField()),
                ('actif', models.BooleanField(default=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('date_modification', models.DateTimeField(auto_now=True)),
                ('article', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commentaires', to='blog.article')),
                ('auteur', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reponses', to='blog.commentaire')),
            ],
            options={
                'verbose_name': 'Commentaire',
                'verbose_name_plural': 'Commentaires',
                'ordering': ['date_creation'],
            },
        ),
    ]

from django.db import models
from django.conf import settings
from django.utils import timezone
from common.models import BaseProduct, Categorie


class Service(BaseProduct):
    """Modèle pour les services proposés par la librairie"""

    TYPE_CHOICES = [
        ('formation', 'Formation'),
        ('consultation', 'Consultation'),
        ('reparation', 'Réparation'),
        ('installation', 'Installation'),
        ('maintenance', 'Maintenance'),
        ('personnalisation', 'Personnalisation'),
        ('livraison', 'Livraison'),
        ('autre', 'Autre service'),
    ]

    DUREE_CHOICES = [
        ('30min', '30 minutes'),
        ('1h', '1 heure'),
        ('2h', '2 heures'),
        ('demi_journee', 'Demi-journée'),
        ('journee', 'Journée complète'),
        ('plusieurs_jours', 'Plusieurs jours'),
        ('sur_mesure', 'Sur mesure'),
    ]

    LIEU_CHOICES = [
        ('domicile', 'À domicile'),
        ('magasin', 'En magasin'),
        ('distance', 'À distance'),
        ('entreprise', 'En entreprise'),
        ('flexible', 'Flexible'),
    ]

    # Informations spécifiques au service
    type_service = models.CharField(max_length=20, choices=TYPE_CHOICES)
    categorie = models.ForeignKey(Categorie, on_delete=models.SET_NULL, null=True, blank=True)

    # Caractéristiques du service
    duree_estimee = models.CharField(max_length=20, choices=DUREE_CHOICES)
    lieu_prestation = models.CharField(max_length=20, choices=LIEU_CHOICES)
    materiel_inclus = models.BooleanField(default=False, help_text='Matériel inclus dans le service')
    deplacement_inclus = models.BooleanField(default=False, help_text='Déplacement inclus')

    # Détails du service
    prerequis = models.TextField(blank=True, help_text='Prérequis pour bénéficier du service')
    materiel_necessaire = models.TextField(blank=True, help_text='Matériel nécessaire côté client')
    deroulement = models.TextField(help_text='Déroulement du service')

    # Tarification
    prix_deplacement = models.DecimalField(max_digits=8, decimal_places=2, default=0.00)
    prix_materiel = models.DecimalField(max_digits=8, decimal_places=2, default=0.00)

    # Disponibilité
    disponible_weekend = models.BooleanField(default=False)
    disponible_soir = models.BooleanField(default=False)
    delai_reservation = models.PositiveIntegerField(default=1, help_text='Délai minimum de réservation en jours')

    # Images
    image_2 = models.ImageField(upload_to='services/', blank=True, null=True)
    image_3 = models.ImageField(upload_to='services/', blank=True, null=True)

    # Statistiques
    note_moyenne = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    nombre_avis = models.PositiveIntegerField(default=0)
    nombre_prestations = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"{self.nom} - {self.get_type_service_display()}"

    @property
    def prix_total_estime(self):
        """Prix total estimé incluant déplacement et matériel"""
        return self.prix + self.prix_deplacement + self.prix_materiel

    class Meta:
        verbose_name = "Service"
        verbose_name_plural = "Services"


class ReservationService(models.Model):
    """Réservations de services"""

    STATUS_CHOICES = [
        ('demande', 'Demande en cours'),
        ('confirmee', 'Confirmée'),
        ('en_cours', 'En cours'),
        ('terminee', 'Terminée'),
        ('annulee', 'Annulée'),
        ('reportee', 'Reportée'),
    ]

    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='reservations')
    client = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='reservations_services')
    prestataire = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='prestations')

    # Informations de réservation
    date_souhaitee = models.DateTimeField()
    date_confirmee = models.DateTimeField(null=True, blank=True)
    duree_reelle = models.DurationField(null=True, blank=True)

    # Lieu et contact
    adresse_prestation = models.TextField(blank=True)
    telephone_contact = models.CharField(max_length=20)

    # Détails de la demande
    description_besoin = models.TextField(help_text='Description détaillée du besoin')
    materiel_client = models.TextField(blank=True, help_text='Matériel disponible côté client')
    contraintes = models.TextField(blank=True, help_text='Contraintes particulières')

    # Statut et suivi
    statut = models.CharField(max_length=20, choices=STATUS_CHOICES, default='demande')
    prix_final = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    notes_prestataire = models.TextField(blank=True)

    # Dates
    date_creation = models.DateTimeField(auto_now_add=True)
    date_modification = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Réservation {self.service.nom} - {self.client.email}"

    class Meta:
        verbose_name = "Réservation service"
        verbose_name_plural = "Réservations services"
        ordering = ['-date_creation']


class AvisService(models.Model):
    """Avis sur les services"""

    NOTES_CHOICES = [
        (1, '1 étoile'),
        (2, '2 étoiles'),
        (3, '3 étoiles'),
        (4, '4 étoiles'),
        (5, '5 étoiles'),
    ]

    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='avis')
    reservation = models.OneToOneField(ReservationService, on_delete=models.CASCADE, related_name='avis')
    client = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)

    # Notes détaillées
    note_globale = models.PositiveSmallIntegerField(choices=NOTES_CHOICES)
    note_ponctualite = models.PositiveSmallIntegerField(choices=NOTES_CHOICES, blank=True, null=True)
    note_competence = models.PositiveSmallIntegerField(choices=NOTES_CHOICES, blank=True, null=True)
    note_communication = models.PositiveSmallIntegerField(choices=NOTES_CHOICES, blank=True, null=True)
    note_rapport_qualite_prix = models.PositiveSmallIntegerField(choices=NOTES_CHOICES, blank=True, null=True)

    # Commentaires
    commentaire = models.TextField(blank=True)
    points_positifs = models.TextField(blank=True)
    points_amelioration = models.TextField(blank=True)

    # Recommandation
    recommande = models.BooleanField(default=True)

    date_creation = models.DateTimeField(auto_now_add=True)
    verifie = models.BooleanField(default=False)

    def __str__(self):
        return f"Avis de {self.client.email} sur {self.service.nom} - {self.note_globale}/5"

    class Meta:
        verbose_name = "Avis service"
        verbose_name_plural = "Avis services"
        ordering = ['-date_creation']


class CreneauDisponibilite(models.Model):
    """Créneaux de disponibilité pour les services"""

    JOUR_CHOICES = [
        (0, 'Lundi'),
        (1, 'Mardi'),
        (2, 'Mercredi'),
        (3, 'Jeudi'),
        (4, 'Vendredi'),
        (5, 'Samedi'),
        (6, 'Dimanche'),
    ]

    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='creneaux')
    prestataire = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='creneaux_disponibilite')

    jour_semaine = models.PositiveSmallIntegerField(choices=JOUR_CHOICES)
    heure_debut = models.TimeField()
    heure_fin = models.TimeField()
    actif = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.get_jour_semaine_display()} {self.heure_debut}-{self.heure_fin}"

    class Meta:
        verbose_name = "Créneau disponibilité"
        verbose_name_plural = "Créneaux disponibilité"
        unique_together = ['service', 'prestataire', 'jour_semaine', 'heure_debut']

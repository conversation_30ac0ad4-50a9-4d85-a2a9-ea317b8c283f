# Generated by Django 5.1.5 on 2025-08-06 22:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('common', '__first__'),
    ]

    operations = [
        migrations.CreateModel(
            name='AvisAccessoireInformatique',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.PositiveSmallIntegerField(choices=[(1, '1 étoile'), (2, '2 étoiles'), (3, '3 étoiles'), (4, '4 étoiles'), (5, '5 étoiles')])),
                ('commentaire', models.TextField(blank=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('verifie', models.BooleanField(default=False)),
                ('note_qualite', models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 étoile'), (2, '2 étoiles'), (3, '3 étoiles'), (4, '4 étoiles'), (5, '5 étoiles')], null=True)),
                ('note_facilite_utilisation', models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 étoile'), (2, '2 étoiles'), (3, '3 étoiles'), (4, '4 étoiles'), (5, '5 étoiles')], null=True)),
                ('note_rapport_qualite_prix', models.PositiveSmallIntegerField(blank=True, choices=[(1, '1 étoile'), (2, '2 étoiles'), (3, '3 étoiles'), (4, '4 étoiles'), (5, '5 étoiles')], null=True)),
            ],
            options={
                'verbose_name': 'Avis accessoire informatique',
                'verbose_name_plural': 'Avis accessoires informatique',
                'ordering': ['-date_creation'],
            },
        ),
        migrations.CreateModel(
            name='GuideCompatibilite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('systeme', models.CharField(help_text='Ex: Windows 11, macOS Monterey', max_length=100)),
                ('version_minimum', models.CharField(blank=True, max_length=50)),
                ('notes_compatibilite', models.TextField(blank=True)),
                ('teste', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'Guide compatibilité',
                'verbose_name_plural': 'Guides compatibilité',
            },
        ),
        migrations.CreateModel(
            name='MarqueInformatique',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='marques_informatique/')),
                ('site_web', models.URLField(blank=True)),
                ('support_technique', models.URLField(blank=True)),
            ],
            options={
                'verbose_name': 'Marque informatique',
                'verbose_name_plural': 'Marques informatique',
            },
        ),
        migrations.CreateModel(
            name='AccessoireInformatique',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('prix', models.DecimalField(decimal_places=2, max_digits=10)),
                ('stock', models.PositiveIntegerField(default=0)),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/')),
                ('actif', models.BooleanField(default=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('date_modification', models.DateTimeField(auto_now=True)),
                ('type_accessoire', models.CharField(choices=[('souris', 'Souris'), ('clavier', 'Claviers'), ('ecran', 'Écrans et moniteurs'), ('casque', 'Casques et audio'), ('webcam', 'Webcams'), ('stockage', 'Stockage (USB, disques)'), ('cable', 'Câbles et adaptateurs'), ('hub', 'Hubs et docks'), ('protection', 'Protection et nettoyage'), ('gaming', 'Gaming'), ('reseau', 'Réseau'), ('alimentation', 'Alimentation'), ('refroidissement', 'Refroidissement'), ('autre', 'Autres accessoires')], max_length=20)),
                ('reference', models.CharField(blank=True, help_text='Référence fabricant', max_length=50)),
                ('modele', models.CharField(blank=True, max_length=100)),
                ('couleur', models.CharField(blank=True, max_length=50)),
                ('dimensions', models.CharField(blank=True, help_text='Ex: 12 x 8 x 3 cm', max_length=100)),
                ('poids', models.CharField(blank=True, help_text='Ex: 150g', max_length=20)),
                ('compatibilite', models.CharField(choices=[('windows', 'Windows'), ('mac', 'Mac'), ('linux', 'Linux'), ('android', 'Android'), ('ios', 'iOS'), ('universel', 'Universel')], default='universel', max_length=20)),
                ('connectivite', models.CharField(blank=True, help_text='Ex: USB-C, Bluetooth 5.0, WiFi', max_length=100)),
                ('alimentation', models.CharField(blank=True, help_text='Ex: USB, Batterie, Secteur', max_length=100)),
                ('specifications_techniques', models.TextField(blank=True)),
                ('contenu_boite', models.TextField(blank=True, help_text='Contenu de la boîte')),
                ('image_2', models.ImageField(blank=True, null=True, upload_to='accessoires_informatique/')),
                ('image_3', models.ImageField(blank=True, null=True, upload_to='accessoires_informatique/')),
                ('image_4', models.ImageField(blank=True, null=True, upload_to='accessoires_informatique/')),
                ('garantie', models.CharField(blank=True, help_text='Ex: 2 ans constructeur', max_length=50)),
                ('certification', models.CharField(blank=True, help_text='Ex: CE, FCC, RoHS', max_length=100)),
                ('note_moyenne', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('nombre_avis', models.PositiveIntegerField(default=0)),
                ('categorie', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.categorie')),
            ],
            options={
                'verbose_name': 'Accessoire informatique',
                'verbose_name_plural': 'Accessoires informatique',
            },
        ),
    ]

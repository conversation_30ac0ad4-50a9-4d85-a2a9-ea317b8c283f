# Generated by Django 5.1.5 on 2025-08-06 22:21

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('common', '__first__'),
        ('librairie', '0001_initial'),
        ('livres_electroniques', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='collection',
            name='editeur',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='collections', to='livres_electroniques.editeur'),
        ),
        migrations.AddField(
            model_name='livrephysique',
            name='auteurs',
            field=models.ManyToManyField(related_name='livres_physiques', to='livres_electroniques.auteur'),
        ),
        migrations.AddField(
            model_name='livrephysique',
            name='categorie',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.categorie'),
        ),
        migrations.AddField(
            model_name='livrephysique',
            name='editeur',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='livres_electroniques.editeur'),
        ),
        migrations.AddField(
            model_name='livrephysique',
            name='vendeur',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_products', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='avislivrephysique',
            name='livre',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='avis', to='librairie.livrephysique'),
        ),
        migrations.AddField(
            model_name='reservation',
            name='client',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reservations', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='reservation',
            name='livre',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reservations', to='librairie.livrephysique'),
        ),
        migrations.AlterUniqueTogether(
            name='avislivrephysique',
            unique_together={('livre', 'client')},
        ),
        migrations.AlterUniqueTogether(
            name='reservation',
            unique_together={('livre', 'client')},
        ),
    ]

import random
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.utils.text import slugify

from livres_electroniques.models import LivreElectronique
from librairie.models import LivrePhysique
from accessoires_bureautique.models import AccessoireBureautique
from accessoires_informatique.models import AccessoireInformatique
from services.models import Service


class Command(BaseCommand):
    help = 'Améliore les données importées avec des prix et descriptions réalistes'

    def handle(self, *args, **options):
        self.stdout.write('Amélioration des données importées...')
        
        self.enhance_livres_physiques()
        self.enhance_accessoires_bureautique()
        self.enhance_services()
        
        self.stdout.write(
            self.style.SUCCESS('Amélioration terminée avec succès!')
        )

    def enhance_livres_physiques(self):
        """Améliore les données des livres physiques"""
        self.stdout.write('Amélioration des livres physiques...')
        
        for livre in LivrePhysique.objects.all():
            # Générer un prix réaliste basé sur le nom
            if 'archive' in livre.nom.lower() or 'boite' in livre.nom.lower():
                livre.prix = Decimal(str(random.uniform(5.0, 15.0)))
                livre.stock = random.randint(10, 50)
                livre.resume = f"Boîte d'archivage de qualité professionnelle. {livre.nom} - Solution idéale pour l'organisation et le stockage de vos documents importants."
                livre.format_livre = 'AUTRE'
                livre.etat = 'NEUF'
            else:
                livre.prix = Decimal(str(random.uniform(15.0, 45.0)))
                livre.stock = random.randint(5, 25)
                livre.resume = f"Livre de qualité. {livre.nom} - Une lecture enrichissante pour tous les passionnés."
            
            livre.save()
            self.stdout.write(f'Livre amélioré: {livre.nom} - {livre.prix}€')

    def enhance_accessoires_bureautique(self):
        """Améliore les données des accessoires bureautique"""
        self.stdout.write('Amélioration des accessoires bureautique...')
        
        prix_mapping = {
            'ensemble': (25.0, 75.0),
            'bureau': (15.0, 50.0),
            'ramette': (8.0, 15.0),
            'papier': (5.0, 12.0),
            'stylo': (1.0, 5.0),
            'crayon': (0.5, 3.0),
            'classeur': (3.0, 10.0),
            'chemise': (0.5, 2.0),
        }
        
        type_mapping = {
            'ensemble': 'AUTRE',
            'bureau': 'AUTRE',
            'ramette': 'PAPIER',
            'papier': 'PAPIER',
            'stylo': 'STYLO',
            'crayon': 'CRAYON',
            'classeur': 'CLASSEUR',
            'chemise': 'AUTRE',
        }
        
        for accessoire in AccessoireBureautique.objects.all():
            nom_lower = accessoire.nom.lower()
            
            # Déterminer le prix basé sur le type de produit
            prix_min, prix_max = (10.0, 30.0)  # Valeur par défaut
            type_acc = 'AUTRE'
            
            for keyword, (min_p, max_p) in prix_mapping.items():
                if keyword in nom_lower:
                    prix_min, prix_max = min_p, max_p
                    type_acc = type_mapping.get(keyword, 'AUTRE')
                    break
            
            accessoire.prix = Decimal(str(random.uniform(prix_min, prix_max)))
            accessoire.stock = random.randint(15, 100)
            accessoire.type_accessoire = type_acc
            
            # Améliorer la description
            if 'ensemble' in nom_lower:
                accessoire.description = f"Ensemble complet de bureau professionnel. {accessoire.nom} - Comprend tous les éléments essentiels pour un espace de travail organisé et efficace."
            elif 'ramette' in nom_lower or 'papier' in nom_lower:
                accessoire.description = f"Papier de haute qualité pour impression et écriture. {accessoire.nom} - Format A4, grammage optimal pour tous vos besoins bureautiques."
            else:
                accessoire.description = f"Accessoire de bureau de qualité professionnelle. {accessoire.nom} - Indispensable pour votre organisation quotidienne."
            
            # Ajouter des couleurs aléatoires
            couleurs = ['BLANC', 'NOIR', 'BLEU', 'ROUGE', 'VERT', 'GRIS']
            accessoire.couleur = random.choice(couleurs)
            
            accessoire.save()
            self.stdout.write(f'Accessoire amélioré: {accessoire.nom} - {accessoire.prix}€')

    def enhance_services(self):
        """Améliore les données des services"""
        self.stdout.write('Amélioration des services...')
        
        service_mapping = {
            'archive': {
                'type': 'ORGANISATION',
                'prix': (20.0, 50.0),
                'duree': '2H',
                'description': 'Service professionnel d\'archivage et d\'organisation de documents.'
            },
            'boite': {
                'type': 'ORGANISATION', 
                'prix': (15.0, 35.0),
                'duree': '1H',
                'description': 'Service de fourniture et installation de solutions de rangement.'
            },
            'enveloppe': {
                'type': 'AUTRE',
                'prix': (10.0, 25.0),
                'duree': '30MIN',
                'description': 'Service de préparation et fourniture d\'enveloppes personnalisées.'
            }
        }
        
        for service in Service.objects.all():
            nom_lower = service.nom.lower()
            
            # Déterminer le type et prix basé sur le nom
            service_info = service_mapping.get('archive', service_mapping['archive'])  # Défaut
            
            for keyword, info in service_mapping.items():
                if keyword in nom_lower:
                    service_info = info
                    break
            
            prix_min, prix_max = service_info['prix']
            service.prix = Decimal(str(random.uniform(prix_min, prix_max)))
            service.type_service = service_info['type']
            service.duree_estimee = service_info['duree']
            service.description = f"{service_info['description']} {service.nom} - Service de qualité professionnelle adapté à vos besoins spécifiques."
            
            # Ajouter des détails supplémentaires
            service.lieu_prestation = 'SUR_SITE'
            service.disponible_weekend = random.choice([True, False])
            service.disponible_soir = random.choice([True, False])
            
            service.save()
            self.stdout.write(f'Service amélioré: {service.nom} - {service.prix}€')

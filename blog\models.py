from django.db import models
from django.conf import settings
from django.urls import reverse
from django.utils.text import slugify
from ckeditor.fields import RichTextField
from taggit.managers import TaggableManager


class CategorieArticle(models.Model):
    """Catégories pour les articles de blog"""

    nom = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True, blank=True)
    description = models.TextField(blank=True)
    couleur = models.CharField(max_length=7, default='#007bff', help_text='Code couleur hexadécimal')
    actif = models.BooleanField(default=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.nom)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.nom

    class Meta:
        verbose_name = "Catégorie d'article"
        verbose_name_plural = "Catégories d'articles"


class Article(models.Model):
    """Articles de blog pour diffuser les activités de la librairie"""

    STATUS_CHOICES = [
        ('brouillon', 'Brouillon'),
        ('publie', 'Publié'),
        ('archive', 'Archivé'),
    ]

    titre = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True, blank=True)
    auteur = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='articles')
    categorie = models.ForeignKey(CategorieArticle, on_delete=models.SET_NULL, null=True, blank=True)
    contenu = RichTextField()
    extrait = models.TextField(max_length=300, help_text='Résumé de l\'article (300 caractères max)')
    image_principale = models.ImageField(upload_to='blog/articles/', blank=True, null=True)
    statut = models.CharField(max_length=20, choices=STATUS_CHOICES, default='brouillon')
    featured = models.BooleanField(default=False, verbose_name='Article à la une')

    # SEO
    meta_description = models.CharField(max_length=160, blank=True, help_text='Description pour les moteurs de recherche')
    meta_keywords = models.CharField(max_length=255, blank=True, help_text='Mots-clés séparés par des virgules')

    # Dates
    date_creation = models.DateTimeField(auto_now_add=True)
    date_modification = models.DateTimeField(auto_now=True)
    date_publication = models.DateTimeField(null=True, blank=True)

    # Statistiques
    vues = models.PositiveIntegerField(default=0)

    # Tags
    tags = TaggableManager(blank=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.titre)
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('blog:article_detail', kwargs={'slug': self.slug})

    def __str__(self):
        return self.titre

    class Meta:
        verbose_name = "Article"
        verbose_name_plural = "Articles"
        ordering = ['-date_publication', '-date_creation']


class Commentaire(models.Model):
    """Commentaires sur les articles de blog"""

    article = models.ForeignKey(Article, on_delete=models.CASCADE, related_name='commentaires')
    auteur = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    contenu = models.TextField()
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='reponses')
    actif = models.BooleanField(default=True)
    date_creation = models.DateTimeField(auto_now_add=True)
    date_modification = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f'Commentaire de {self.auteur.email} sur {self.article.titre}'

    class Meta:
        verbose_name = "Commentaire"
        verbose_name_plural = "Commentaires"
        ordering = ['date_creation']


class Newsletter(models.Model):
    """Abonnements à la newsletter"""

    email = models.EmailField(unique=True)
    nom = models.CharField(max_length=100, blank=True)
    actif = models.BooleanField(default=True)
    date_inscription = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.email

    class Meta:
        verbose_name = "Abonnement newsletter"
        verbose_name_plural = "Abonnements newsletter"

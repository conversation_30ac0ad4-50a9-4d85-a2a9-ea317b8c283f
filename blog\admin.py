from django.contrib import admin
from django.utils.html import format_html
from .models import CategorieArticle, Article, Commentaire, Newsletter


@admin.register(CategorieArticle)
class CategorieArticleAdmin(admin.ModelAdmin):
    list_display = ('nom', 'couleur_display', 'actif', 'description')
    list_filter = ('actif',)
    search_fields = ('nom', 'description')
    prepopulated_fields = {'slug': ('nom',)}
    list_editable = ('actif',)

    def couleur_display(self, obj):
        return format_html(
            '<span style="background-color: {}; padding: 2px 8px; border-radius: 3px; color: white;">{}</span>',
            obj.couleur,
            obj.couleur
        )
    couleur_display.short_description = 'Couleur'


@admin.register(Article)
class ArticleAdmin(admin.ModelAdmin):
    list_display = ('titre', 'auteur', 'categorie', 'statut', 'featured', 'vues', 'date_publication', 'date_creation')
    list_filter = ('statut', 'featured', 'categorie', 'date_creation', 'date_publication')
    search_fields = ('titre', 'contenu', 'auteur__email', 'auteur__first_name', 'auteur__last_name')
    prepopulated_fields = {'slug': ('titre',)}
    list_editable = ('statut', 'featured')
    readonly_fields = ('vues', 'date_creation', 'date_modification')
    # filter_horizontal = ('tags',)  # Désactivé car TaggableManager ne le supporte pas

    fieldsets = (
        ('Informations principales', {
            'fields': ('titre', 'slug', 'auteur', 'categorie')
        }),
        ('Contenu', {
            'fields': ('extrait', 'contenu', 'image_principale')
        }),
        ('Publication', {
            'fields': ('statut', 'featured', 'date_publication')
        }),
        ('SEO', {
            'fields': ('meta_description', 'meta_keywords'),
            'classes': ('collapse',)
        }),
        ('Tags', {
            'fields': ('tags',)
        }),
        ('Statistiques', {
            'fields': ('vues', 'date_creation', 'date_modification'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Si c'est un nouvel article
            obj.auteur = request.user
        super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser or request.user.role == 'admin':
            return qs
        return qs.filter(auteur=request.user)


@admin.register(Commentaire)
class CommentaireAdmin(admin.ModelAdmin):
    list_display = ('article', 'auteur', 'contenu_court', 'actif', 'date_creation')
    list_filter = ('actif', 'date_creation', 'article__categorie')
    search_fields = ('contenu', 'auteur__email', 'article__titre')
    list_editable = ('actif',)
    readonly_fields = ('date_creation', 'date_modification')

    def contenu_court(self, obj):
        return obj.contenu[:50] + '...' if len(obj.contenu) > 50 else obj.contenu
    contenu_court.short_description = 'Contenu'


@admin.register(Newsletter)
class NewsletterAdmin(admin.ModelAdmin):
    list_display = ('email', 'nom', 'actif', 'date_inscription')
    list_filter = ('actif', 'date_inscription')
    search_fields = ('email', 'nom')
    list_editable = ('actif',)
    readonly_fields = ('date_inscription',)

    actions = ['activer_abonnements', 'desactiver_abonnements']

    def activer_abonnements(self, request, queryset):
        updated = queryset.update(actif=True)
        self.message_user(request, f'{updated} abonnements activés.')
    activer_abonnements.short_description = "Activer les abonnements sélectionnés"

    def desactiver_abonnements(self, request, queryset):
        updated = queryset.update(actif=False)
        self.message_user(request, f'{updated} abonnements désactivés.')
    desactiver_abonnements.short_description = "Désactiver les abonnements sélectionnés"

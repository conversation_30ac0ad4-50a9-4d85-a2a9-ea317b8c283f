# Generated by Django 5.1.5 on 2025-08-06 22:21

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('common', '__first__'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Auteur',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100)),
                ('prenom', models.CharField(max_length=100)),
                ('biographie', models.TextField(blank=True)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='auteurs/')),
                ('site_web', models.URLField(blank=True)),
                ('date_naissance', models.DateField(blank=True, null=True)),
                ('nationalite', models.Char<PERSON>ield(blank=True, max_length=50)),
            ],
            options={
                'verbose_name': 'Auteur',
                'verbose_name_plural': 'Auteurs',
                'ordering': ['nom', 'prenom'],
            },
        ),
        migrations.CreateModel(
            name='Editeur',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('site_web', models.URLField(blank=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='editeurs/')),
            ],
            options={
                'verbose_name': 'Éditeur',
                'verbose_name_plural': 'Éditeurs',
            },
        ),
        migrations.CreateModel(
            name='LivreElectronique',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('prix', models.DecimalField(decimal_places=2, max_digits=10)),
                ('stock', models.PositiveIntegerField(default=0)),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/')),
                ('actif', models.BooleanField(default=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('date_modification', models.DateTimeField(auto_now=True)),
                ('isbn', models.CharField(blank=True, max_length=13, null=True, unique=True)),
                ('format_fichier', models.CharField(choices=[('pdf', 'PDF'), ('epub', 'EPUB'), ('mobi', 'MOBI'), ('azw', 'AZW')], default='pdf', max_length=10)),
                ('taille_fichier', models.CharField(blank=True, help_text='Ex: 2.5 MB', max_length=20)),
                ('nombre_pages', models.PositiveIntegerField(blank=True, null=True)),
                ('langue', models.CharField(choices=[('fr', 'Français'), ('en', 'Anglais'), ('es', 'Espagnol'), ('de', 'Allemand'), ('it', 'Italien')], default='fr', max_length=5)),
                ('fichier_livre', models.FileField(blank=True, null=True, upload_to='livres_electroniques/')),
                ('extrait_gratuit', models.FileField(blank=True, null=True, upload_to='extraits/')),
                ('couverture', models.ImageField(blank=True, null=True, upload_to='couvertures/')),
                ('date_publication', models.DateField(blank=True, null=True)),
                ('resume', models.TextField(help_text='Résumé du livre')),
                ('table_matieres', models.TextField(blank=True, help_text='Table des matières')),
                ('nombre_telechargements', models.PositiveIntegerField(default=0)),
                ('note_moyenne', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('nombre_avis', models.PositiveIntegerField(default=0)),
                ('auteurs', models.ManyToManyField(related_name='livres_electroniques', to='livres_electroniques.auteur')),
                ('categorie', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='common.categorie')),
                ('editeur', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='livres_electroniques.editeur')),
                ('vendeur', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_products', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Livre électronique',
                'verbose_name_plural': 'Livres électroniques',
            },
        ),
        migrations.CreateModel(
            name='TelechargementLivre',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_telechargement', models.DateTimeField(auto_now_add=True)),
                ('adresse_ip', models.GenericIPAddressField()),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('livre', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='telechargements', to='livres_electroniques.livreelectronique')),
            ],
            options={
                'verbose_name': 'Téléchargement livre',
                'verbose_name_plural': 'Téléchargements livres',
                'ordering': ['-date_telechargement'],
            },
        ),
        migrations.CreateModel(
            name='AvisLivre',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.PositiveSmallIntegerField(choices=[(1, '1 étoile'), (2, '2 étoiles'), (3, '3 étoiles'), (4, '4 étoiles'), (5, '5 étoiles')])),
                ('commentaire', models.TextField(blank=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('verifie', models.BooleanField(default=False, help_text='Avis vérifié par un modérateur')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('livre', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='avis', to='livres_electroniques.livreelectronique')),
            ],
            options={
                'verbose_name': 'Avis livre',
                'verbose_name_plural': 'Avis livres',
                'ordering': ['-date_creation'],
                'unique_together': {('livre', 'client')},
            },
        ),
    ]

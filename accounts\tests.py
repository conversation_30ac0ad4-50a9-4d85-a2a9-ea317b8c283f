from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from common.models import <PERSON><PERSON>, ItemPanier

User = get_user_model()


class UserModelTest(TestCase):
    """Tests pour le modèle User personnalisé"""

    def setUp(self):
        self.user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123'
        }

    def test_create_user(self):
        """Test de création d'un utilisateur"""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.role, 'client')  # Rôle par défaut
        self.assertTrue(user.is_client())
        self.assertFalse(user.is_admin())
        self.assertFalse(user.is_vendeur())

    def test_create_admin_user(self):
        """Test de création d'un administrateur"""
        admin_data = self.user_data.copy()
        admin_data['role'] = 'admin'
        user = User.objects.create_user(**admin_data)
        self.assertTrue(user.is_admin())
        self.assertFalse(user.is_client())

    def test_create_vendeur_user(self):
        """Test de création d'un vendeur"""
        vendeur_data = self.user_data.copy()
        vendeur_data['role'] = 'vendeur'
        user = User.objects.create_user(**vendeur_data)
        self.assertTrue(user.is_vendeur())
        self.assertFalse(user.is_client())


class AuthenticationTest(TestCase):
    """Tests pour l'authentification"""

    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_login_page(self):
        """Test d'accès à la page de connexion"""
        response = self.client.get(reverse('accounts:login'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Connexion')

    def test_login_success(self):
        """Test de connexion réussie"""
        response = self.client.post(reverse('accounts:login'), {
            'username': '<EMAIL>',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)  # Redirection après connexion

    def test_login_failure(self):
        """Test de connexion échouée"""
        response = self.client.post(reverse('accounts:login'), {
            'username': '<EMAIL>',
            'password': 'wrongpassword'
        })
        self.assertEqual(response.status_code, 200)  # Reste sur la page de connexion

    def test_dashboard_access_authenticated(self):
        """Test d'accès au tableau de bord pour un utilisateur connecté"""
        self.client.login(username='<EMAIL>', password='testpass123')
        response = self.client.get(reverse('accounts:dashboard'))
        self.assertEqual(response.status_code, 200)

    def test_dashboard_access_anonymous(self):
        """Test d'accès au tableau de bord pour un utilisateur anonyme"""
        response = self.client.get(reverse('accounts:dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirection vers login


class PanierTest(TestCase):
    """Tests pour le système de panier"""

    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.login(username='<EMAIL>', password='testpass123')

    def test_panier_creation(self):
        """Test de création automatique du panier"""
        panier, created = Panier.objects.get_or_create(client=self.user)
        self.assertTrue(created)
        self.assertEqual(panier.client, self.user)
        self.assertEqual(panier.total, 0)
        self.assertEqual(panier.nombre_items, 0)

    def test_voir_panier_empty(self):
        """Test d'affichage du panier vide"""
        response = self.client.get(reverse('common:voir_panier'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Votre panier est vide')

    def test_panier_access_anonymous(self):
        """Test d'accès au panier pour un utilisateur anonyme"""
        self.client.logout()
        response = self.client.get(reverse('common:voir_panier'))
        self.assertEqual(response.status_code, 302)  # Redirection vers login
